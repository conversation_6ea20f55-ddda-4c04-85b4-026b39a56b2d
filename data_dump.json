[{"model": "admin.logentry", "pk": 1, "fields": {"action_time": "2025-05-29T10:32:03.045Z", "user": 3, "content_type": 12, "object_id": "1", "object_repr": "Survival", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 2, "fields": {"action_time": "2025-05-29T10:32:08.596Z", "user": 3, "content_type": 12, "object_id": "2", "object_repr": "Bedrock", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 3, "fields": {"action_time": "2025-05-29T10:39:19.263Z", "user": 3, "content_type": 13, "object_id": "1", "object_repr": "Raiden", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 4, "fields": {"action_time": "2025-05-29T10:39:26.442Z", "user": 3, "content_type": 13, "object_id": "2", "object_repr": "<PERSON>", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 5, "fields": {"action_time": "2025-05-29T10:39:31.214Z", "user": 3, "content_type": 13, "object_id": "3", "object_repr": "SinaZK", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 6, "fields": {"action_time": "2025-05-29T10:39:42.332Z", "user": 3, "content_type": 14, "object_id": "1", "object_repr": "Windows", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 7, "fields": {"action_time": "2025-05-29T10:39:47.292Z", "user": 3, "content_type": 14, "object_id": "2", "object_repr": "Android", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 8, "fields": {"action_time": "2025-05-29T10:40:22.721Z", "user": 3, "content_type": 15, "object_id": "1", "object_repr": "Main (***********:25556)", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 9, "fields": {"action_time": "2025-05-29T10:40:55.924Z", "user": 3, "content_type": 15, "object_id": "2", "object_repr": "<PERSON><PERSON> (***********:25557)", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 10, "fields": {"action_time": "2025-05-29T10:41:57.184Z", "user": 3, "content_type": 16, "object_id": "1", "object_repr": "Item 1", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 11, "fields": {"action_time": "2025-05-29T10:42:26.866Z", "user": 3, "content_type": 16, "object_id": "2", "object_repr": "Item 2", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 12, "fields": {"action_time": "2025-05-29T10:43:31.525Z", "user": 3, "content_type": 16, "object_id": "3", "object_repr": "Item 3", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 13, "fields": {"action_time": "2025-05-29T13:49:05.369Z", "user": 3, "content_type": 12, "object_id": "2", "object_repr": "Bedrock", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Display name\", \"Description\"]}}]"}}, {"model": "admin.logentry", "pk": 14, "fields": {"action_time": "2025-05-29T13:49:16.056Z", "user": 3, "content_type": 12, "object_id": "1", "object_repr": "Survival", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Display name\", \"Description\"]}}]"}}, {"model": "admin.logentry", "pk": 15, "fields": {"action_time": "2025-05-29T13:49:41.012Z", "user": 3, "content_type": 13, "object_id": "3", "object_repr": "SinaZK", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Display name\", \"Description\"]}}]"}}, {"model": "admin.logentry", "pk": 16, "fields": {"action_time": "2025-05-29T13:49:54.241Z", "user": 3, "content_type": 13, "object_id": "2", "object_repr": "<PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Display name\", \"Description\"]}}]"}}, {"model": "admin.logentry", "pk": 17, "fields": {"action_time": "2025-05-29T13:50:55.947Z", "user": 3, "content_type": 13, "object_id": "1", "object_repr": "<PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\", \"Display name\"]}}]"}}, {"model": "admin.logentry", "pk": 18, "fields": {"action_time": "2025-05-29T19:19:37.424Z", "user": 3, "content_type": 15, "object_id": "1", "object_repr": "Main (***********:25565)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Port\", \"Query port\"]}}]"}}, {"model": "admin.logentry", "pk": 19, "fields": {"action_time": "2025-05-30T07:11:11.747Z", "user": 3, "content_type": 15, "object_id": "1", "object_repr": "Main-Velocity (***********:25565)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\"]}}]"}}, {"model": "admin.logentry", "pk": 20, "fields": {"action_time": "2025-05-30T09:19:09.688Z", "user": 3, "content_type": 12, "object_id": "3", "object_repr": "Ranks", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 21, "fields": {"action_time": "2025-05-30T09:21:03.711Z", "user": 3, "content_type": 16, "object_id": "4", "object_repr": "Iron rank 1m", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 22, "fields": {"action_time": "2025-05-30T19:52:38.411Z", "user": 3, "content_type": 16, "object_id": "4", "object_repr": "Iron rank 1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Description\", \"Image\", \"Ucoin price\"]}}]"}}, {"model": "admin.logentry", "pk": 23, "fields": {"action_time": "2025-05-30T19:54:51.834Z", "user": 3, "content_type": 16, "object_id": "5", "object_repr": "rank-gold-1m", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 24, "fields": {"action_time": "2025-05-30T19:55:17.309Z", "user": 3, "content_type": 16, "object_id": "4", "object_repr": "rank-iron-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\"]}}]"}}, {"model": "admin.logentry", "pk": 25, "fields": {"action_time": "2025-05-30T19:58:04.737Z", "user": 3, "content_type": 16, "object_id": "6", "object_repr": "rank-dia-1m", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 26, "fields": {"action_time": "2025-05-30T20:48:11.955Z", "user": 3, "content_type": 16, "object_id": "7", "object_repr": "rank-emral-1m", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 27, "fields": {"action_time": "2025-05-30T20:49:47.716Z", "user": 3, "content_type": 16, "object_id": "8", "object_repr": "rank-fal-20d", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 28, "fields": {"action_time": "2025-05-30T21:00:45.128Z", "user": 3, "content_type": 16, "object_id": "9", "object_repr": "survival", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 29, "fields": {"action_time": "2025-05-30T21:01:19.352Z", "user": 3, "content_type": 16, "object_id": "9", "object_repr": "survival-key-5k-common", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\"]}}]"}}, {"model": "admin.logentry", "pk": 30, "fields": {"action_time": "2025-05-30T21:02:29.503Z", "user": 3, "content_type": 15, "object_id": "2", "object_repr": "ucraft-lobby (***********:25557)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\", \"Display name\"]}}]"}}, {"model": "admin.logentry", "pk": 31, "fields": {"action_time": "2025-05-30T21:03:07.456Z", "user": 3, "content_type": 15, "object_id": "1", "object_repr": "ucraft-main (***********:25565)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\"]}}]"}}, {"model": "admin.logentry", "pk": 32, "fields": {"action_time": "2025-05-30T21:03:49.183Z", "user": 3, "content_type": 15, "object_id": "3", "object_repr": "ucraft-survival (***********:22557)", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 33, "fields": {"action_time": "2025-05-30T21:04:19.543Z", "user": 3, "content_type": 15, "object_id": "4", "object_repr": "ucraft-bedrock (***********:22558)", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 34, "fields": {"action_time": "2025-05-30T21:04:59.023Z", "user": 3, "content_type": 16, "object_id": "9", "object_repr": "survival-key-5k-common", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 35, "fields": {"action_time": "2025-05-30T21:06:03.930Z", "user": 3, "content_type": 16, "object_id": "10", "object_repr": "survival-key-5k-rare", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 36, "fields": {"action_time": "2025-05-30T21:06:55.752Z", "user": 3, "content_type": 16, "object_id": "11", "object_repr": "survival-key-5k-money", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 37, "fields": {"action_time": "2025-05-30T21:08:35.505Z", "user": 3, "content_type": 16, "object_id": "12", "object_repr": "survival-cube-5*5", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 38, "fields": {"action_time": "2025-05-30T21:10:17.833Z", "user": 3, "content_type": 16, "object_id": "13", "object_repr": "survival-cube-15*15", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 39, "fields": {"action_time": "2025-05-30T21:11:40.138Z", "user": 3, "content_type": 16, "object_id": "14", "object_repr": "survival-key-5k-spawner", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 40, "fields": {"action_time": "2025-05-30T22:03:30.461Z", "user": 3, "content_type": 16, "object_id": "15", "object_repr": "survival-key-5k-legendary", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 41, "fields": {"action_time": "2025-05-30T22:04:15.696Z", "user": 3, "content_type": 16, "object_id": "16", "object_repr": "survival-key-5k-aegis", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 42, "fields": {"action_time": "2025-05-30T22:04:59.856Z", "user": 3, "content_type": 12, "object_id": "2", "object_repr": "BedWars", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\", \"Display name\", \"Description\", \"Enabled\"]}}]"}}, {"model": "admin.logentry", "pk": 43, "fields": {"action_time": "2025-05-30T22:05:54.589Z", "user": 3, "content_type": 12, "object_id": "2", "object_repr": "BedWars", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Published\"]}}]"}}, {"model": "admin.logentry", "pk": 44, "fields": {"action_time": "2025-05-30T22:06:11.059Z", "user": 3, "content_type": 12, "object_id": "2", "object_repr": "BedWars", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Enabled\", \"Published\"]}}]"}}, {"model": "admin.logentry", "pk": 45, "fields": {"action_time": "2025-05-30T22:06:33.191Z", "user": 3, "content_type": 16, "object_id": "3", "object_repr": "Item 3", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Published\"]}}]"}}, {"model": "admin.logentry", "pk": 46, "fields": {"action_time": "2025-05-30T22:07:05.028Z", "user": 3, "content_type": 16, "object_id": "2", "object_repr": "Item 2", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Published\"]}}]"}}, {"model": "admin.logentry", "pk": 47, "fields": {"action_time": "2025-05-30T22:07:11.489Z", "user": 3, "content_type": 16, "object_id": "1", "object_repr": "Item 1", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Published\"]}}]"}}, {"model": "admin.logentry", "pk": 48, "fields": {"action_time": "2025-05-30T22:11:28.047Z", "user": 3, "content_type": 15, "object_id": "4", "object_repr": "ucraft-bedrwars (***********:22558)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\", \"Display name\"]}}]"}}, {"model": "admin.logentry", "pk": 49, "fields": {"action_time": "2025-05-30T22:11:38.600Z", "user": 3, "content_type": 16, "object_id": "17", "object_repr": "bedwars-private-1m", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 50, "fields": {"action_time": "2025-05-30T22:14:43.598Z", "user": 3, "content_type": 16, "object_id": "18", "object_repr": "bedwars-token-50k", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 51, "fields": {"action_time": "2025-05-30T22:15:33.813Z", "user": 3, "content_type": 16, "object_id": "19", "object_repr": "bedwars-token-100k", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 52, "fields": {"action_time": "2025-05-30T22:15:55.829Z", "user": 3, "content_type": 16, "object_id": "20", "object_repr": "bedwars-token-200k", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 53, "fields": {"action_time": "2025-05-30T22:16:48.721Z", "user": 3, "content_type": 16, "object_id": "20", "object_repr": "bedwars-token-200k", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 54, "fields": {"action_time": "2025-06-09T16:20:22.828Z", "user": 3, "content_type": 16, "object_id": "19", "object_repr": "bedwars-token-100k", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 55, "fields": {"action_time": "2025-06-09T16:25:46.796Z", "user": 3, "content_type": 16, "object_id": "20", "object_repr": "bedwars-token-200k", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 56, "fields": {"action_time": "2025-06-09T16:26:17.983Z", "user": 3, "content_type": 16, "object_id": "18", "object_repr": "bedwars-token-50k", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 57, "fields": {"action_time": "2025-06-09T16:26:47.509Z", "user": 3, "content_type": 16, "object_id": "17", "object_repr": "bedwars-private-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 58, "fields": {"action_time": "2025-06-09T16:27:07.604Z", "user": 3, "content_type": 16, "object_id": "16", "object_repr": "survival-key-5k-aegis", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 59, "fields": {"action_time": "2025-06-09T16:27:33.016Z", "user": 3, "content_type": 16, "object_id": "15", "object_repr": "survival-key-5k-legendary", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 60, "fields": {"action_time": "2025-06-09T16:27:56.907Z", "user": 3, "content_type": 16, "object_id": "14", "object_repr": "survival-key-5k-spawner", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 61, "fields": {"action_time": "2025-06-09T16:28:21.478Z", "user": 3, "content_type": 16, "object_id": "13", "object_repr": "survival-cube-15*15", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 62, "fields": {"action_time": "2025-06-09T16:28:42.365Z", "user": 3, "content_type": 16, "object_id": "12", "object_repr": "survival-cube-5*5", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 63, "fields": {"action_time": "2025-06-09T16:29:09.464Z", "user": 3, "content_type": 16, "object_id": "11", "object_repr": "survival-key-5k-money", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 64, "fields": {"action_time": "2025-06-09T16:30:01.178Z", "user": 3, "content_type": 16, "object_id": "10", "object_repr": "survival-key-5k-rare", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 65, "fields": {"action_time": "2025-06-09T16:30:21.367Z", "user": 3, "content_type": 16, "object_id": "9", "object_repr": "survival-key-5k-common", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 66, "fields": {"action_time": "2025-06-09T16:30:46.973Z", "user": 3, "content_type": 16, "object_id": "8", "object_repr": "rank-fal-20d", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 67, "fields": {"action_time": "2025-06-09T16:31:09.038Z", "user": 3, "content_type": 16, "object_id": "7", "object_repr": "rank-emral-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 68, "fields": {"action_time": "2025-06-09T16:31:55.251Z", "user": 3, "content_type": 16, "object_id": "6", "object_repr": "rank-dia-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 69, "fields": {"action_time": "2025-06-09T16:32:32.823Z", "user": 3, "content_type": 16, "object_id": "5", "object_repr": "rank-gold-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 70, "fields": {"action_time": "2025-06-09T16:33:07.086Z", "user": 3, "content_type": 16, "object_id": "4", "object_repr": "rank-iron-1m", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 71, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "13", "object_repr": "raiden - Item 1 [timeout]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 72, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "12", "object_repr": "sinazk - Item 3 [timeout]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 73, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "11", "object_repr": "Sinazk - Item 3 [failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 74, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "10", "object_repr": "Sinazk - Item 3 [failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 75, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "9", "object_repr": "Sinazk - Item 3 [failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 76, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "8", "object_repr": "Sinazk - Item 3 [failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 77, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "7", "object_repr": "Sinazk - Item 3 [finished]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 78, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "6", "object_repr": "raiden - Item 3 [failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 79, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "5", "object_repr": "raiden - Item 2 [command_failed]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 80, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "4", "object_repr": "raiden - Item 2 [timeout]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 81, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "3", "object_repr": "raiden - Item 3 [finished]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 82, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "2", "object_repr": "raiden - Item 1 [timeout]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 83, "fields": {"action_time": "2025-06-09T16:33:44.618Z", "user": 3, "content_type": 17, "object_id": "1", "object_repr": "raiden - Item 1 [timeout]", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 84, "fields": {"action_time": "2025-06-09T16:33:59.169Z", "user": 3, "content_type": 16, "object_id": "3", "object_repr": "Item 3", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 85, "fields": {"action_time": "2025-06-09T16:33:59.169Z", "user": 3, "content_type": 16, "object_id": "2", "object_repr": "Item 2", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 86, "fields": {"action_time": "2025-06-09T16:33:59.169Z", "user": 3, "content_type": 16, "object_id": "1", "object_repr": "Item 1", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 87, "fields": {"action_time": "2025-06-09T16:37:18.947Z", "user": 3, "content_type": 13, "object_id": "3", "object_repr": "SinaZK", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 88, "fields": {"action_time": "2025-06-09T16:38:31.045Z", "user": 3, "content_type": 13, "object_id": "2", "object_repr": "<PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Description\", \"Image\"]}}]"}}, {"model": "admin.logentry", "pk": 89, "fields": {"action_time": "2025-06-09T16:40:03.080Z", "user": 3, "content_type": 13, "object_id": "1", "object_repr": "<PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image\"]}}]"}}, {"model": "auth.permission", "pk": 1, "fields": {"name": "Can add log entry", "content_type": 1, "codename": "add_logentry"}}, {"model": "auth.permission", "pk": 2, "fields": {"name": "Can change log entry", "content_type": 1, "codename": "change_logentry"}}, {"model": "auth.permission", "pk": 3, "fields": {"name": "Can delete log entry", "content_type": 1, "codename": "delete_logentry"}}, {"model": "auth.permission", "pk": 4, "fields": {"name": "Can view log entry", "content_type": 1, "codename": "view_logentry"}}, {"model": "auth.permission", "pk": 5, "fields": {"name": "Can add permission", "content_type": 2, "codename": "add_permission"}}, {"model": "auth.permission", "pk": 6, "fields": {"name": "Can change permission", "content_type": 2, "codename": "change_permission"}}, {"model": "auth.permission", "pk": 7, "fields": {"name": "Can delete permission", "content_type": 2, "codename": "delete_permission"}}, {"model": "auth.permission", "pk": 8, "fields": {"name": "Can view permission", "content_type": 2, "codename": "view_permission"}}, {"model": "auth.permission", "pk": 9, "fields": {"name": "Can add group", "content_type": 3, "codename": "add_group"}}, {"model": "auth.permission", "pk": 10, "fields": {"name": "Can change group", "content_type": 3, "codename": "change_group"}}, {"model": "auth.permission", "pk": 11, "fields": {"name": "Can delete group", "content_type": 3, "codename": "delete_group"}}, {"model": "auth.permission", "pk": 12, "fields": {"name": "Can view group", "content_type": 3, "codename": "view_group"}}, {"model": "auth.permission", "pk": 13, "fields": {"name": "Can add user", "content_type": 4, "codename": "add_user"}}, {"model": "auth.permission", "pk": 14, "fields": {"name": "Can change user", "content_type": 4, "codename": "change_user"}}, {"model": "auth.permission", "pk": 15, "fields": {"name": "Can delete user", "content_type": 4, "codename": "delete_user"}}, {"model": "auth.permission", "pk": 16, "fields": {"name": "Can view user", "content_type": 4, "codename": "view_user"}}, {"model": "auth.permission", "pk": 17, "fields": {"name": "Can add content type", "content_type": 5, "codename": "add_contenttype"}}, {"model": "auth.permission", "pk": 18, "fields": {"name": "Can change content type", "content_type": 5, "codename": "change_contenttype"}}, {"model": "auth.permission", "pk": 19, "fields": {"name": "Can delete content type", "content_type": 5, "codename": "delete_contenttype"}}, {"model": "auth.permission", "pk": 20, "fields": {"name": "Can view content type", "content_type": 5, "codename": "view_contenttype"}}, {"model": "auth.permission", "pk": 21, "fields": {"name": "Can add session", "content_type": 6, "codename": "add_session"}}, {"model": "auth.permission", "pk": 22, "fields": {"name": "Can change session", "content_type": 6, "codename": "change_session"}}, {"model": "auth.permission", "pk": 23, "fields": {"name": "Can delete session", "content_type": 6, "codename": "delete_session"}}, {"model": "auth.permission", "pk": 24, "fields": {"name": "Can view session", "content_type": 6, "codename": "view_session"}}, {"model": "auth.permission", "pk": 25, "fields": {"name": "Can add Scheduled task", "content_type": 7, "codename": "add_schedule"}}, {"model": "auth.permission", "pk": 26, "fields": {"name": "Can change Scheduled task", "content_type": 7, "codename": "change_schedule"}}, {"model": "auth.permission", "pk": 27, "fields": {"name": "Can delete Scheduled task", "content_type": 7, "codename": "delete_schedule"}}, {"model": "auth.permission", "pk": 28, "fields": {"name": "Can view Scheduled task", "content_type": 7, "codename": "view_schedule"}}, {"model": "auth.permission", "pk": 29, "fields": {"name": "Can add task", "content_type": 8, "codename": "add_task"}}, {"model": "auth.permission", "pk": 30, "fields": {"name": "Can change task", "content_type": 8, "codename": "change_task"}}, {"model": "auth.permission", "pk": 31, "fields": {"name": "Can delete task", "content_type": 8, "codename": "delete_task"}}, {"model": "auth.permission", "pk": 32, "fields": {"name": "Can view task", "content_type": 8, "codename": "view_task"}}, {"model": "auth.permission", "pk": 33, "fields": {"name": "Can add Failed task", "content_type": 9, "codename": "add_failure"}}, {"model": "auth.permission", "pk": 34, "fields": {"name": "Can change Failed task", "content_type": 9, "codename": "change_failure"}}, {"model": "auth.permission", "pk": 35, "fields": {"name": "Can delete Failed task", "content_type": 9, "codename": "delete_failure"}}, {"model": "auth.permission", "pk": 36, "fields": {"name": "Can view Failed task", "content_type": 9, "codename": "view_failure"}}, {"model": "auth.permission", "pk": 37, "fields": {"name": "Can add Successful task", "content_type": 10, "codename": "add_success"}}, {"model": "auth.permission", "pk": 38, "fields": {"name": "Can change Successful task", "content_type": 10, "codename": "change_success"}}, {"model": "auth.permission", "pk": 39, "fields": {"name": "Can delete Successful task", "content_type": 10, "codename": "delete_success"}}, {"model": "auth.permission", "pk": 40, "fields": {"name": "Can view Successful task", "content_type": 10, "codename": "view_success"}}, {"model": "auth.permission", "pk": 41, "fields": {"name": "Can add Queued task", "content_type": 11, "codename": "add_ormq"}}, {"model": "auth.permission", "pk": 42, "fields": {"name": "Can change Queued task", "content_type": 11, "codename": "change_ormq"}}, {"model": "auth.permission", "pk": 43, "fields": {"name": "Can delete Queued task", "content_type": 11, "codename": "delete_ormq"}}, {"model": "auth.permission", "pk": 44, "fields": {"name": "Can view Queued task", "content_type": 11, "codename": "view_ormq"}}, {"model": "auth.permission", "pk": 45, "fields": {"name": "Can add category", "content_type": 12, "codename": "add_category"}}, {"model": "auth.permission", "pk": 46, "fields": {"name": "Can change category", "content_type": 12, "codename": "change_category"}}, {"model": "auth.permission", "pk": 47, "fields": {"name": "Can delete category", "content_type": 12, "codename": "delete_category"}}, {"model": "auth.permission", "pk": 48, "fields": {"name": "Can view category", "content_type": 12, "codename": "view_category"}}, {"model": "auth.permission", "pk": 49, "fields": {"name": "Can add content creator", "content_type": 13, "codename": "add_contentcreator"}}, {"model": "auth.permission", "pk": 50, "fields": {"name": "Can change content creator", "content_type": 13, "codename": "change_contentcreator"}}, {"model": "auth.permission", "pk": 51, "fields": {"name": "Can delete content creator", "content_type": 13, "codename": "delete_contentcreator"}}, {"model": "auth.permission", "pk": 52, "fields": {"name": "Can view content creator", "content_type": 13, "codename": "view_contentcreator"}}, {"model": "auth.permission", "pk": 53, "fields": {"name": "Can add download link", "content_type": 14, "codename": "add_downloadlink"}}, {"model": "auth.permission", "pk": 54, "fields": {"name": "Can change download link", "content_type": 14, "codename": "change_downloadlink"}}, {"model": "auth.permission", "pk": 55, "fields": {"name": "Can delete download link", "content_type": 14, "codename": "delete_downloadlink"}}, {"model": "auth.permission", "pk": 56, "fields": {"name": "Can view download link", "content_type": 14, "codename": "view_downloadlink"}}, {"model": "auth.permission", "pk": 57, "fields": {"name": "Can add minecraft server", "content_type": 15, "codename": "add_minecraftserver"}}, {"model": "auth.permission", "pk": 58, "fields": {"name": "Can change minecraft server", "content_type": 15, "codename": "change_minecraftserver"}}, {"model": "auth.permission", "pk": 59, "fields": {"name": "Can delete minecraft server", "content_type": 15, "codename": "delete_minecraftserver"}}, {"model": "auth.permission", "pk": 60, "fields": {"name": "Can view minecraft server", "content_type": 15, "codename": "view_minecraftserver"}}, {"model": "auth.permission", "pk": 61, "fields": {"name": "Can add item", "content_type": 16, "codename": "add_item"}}, {"model": "auth.permission", "pk": 62, "fields": {"name": "Can change item", "content_type": 16, "codename": "change_item"}}, {"model": "auth.permission", "pk": 63, "fields": {"name": "Can delete item", "content_type": 16, "codename": "delete_item"}}, {"model": "auth.permission", "pk": 64, "fields": {"name": "Can view item", "content_type": 16, "codename": "view_item"}}, {"model": "auth.permission", "pk": 65, "fields": {"name": "Can add purchase", "content_type": 17, "codename": "add_purchase"}}, {"model": "auth.permission", "pk": 66, "fields": {"name": "Can change purchase", "content_type": 17, "codename": "change_purchase"}}, {"model": "auth.permission", "pk": 67, "fields": {"name": "Can delete purchase", "content_type": 17, "codename": "delete_purchase"}}, {"model": "auth.permission", "pk": 68, "fields": {"name": "Can view purchase", "content_type": 17, "codename": "view_purchase"}}, {"model": "auth.user", "pk": 1, "fields": {"password": "pbkdf2_sha256$1000000$803Xa7g5iExssUJ6cbot41$LCyA4u/s8E5AffPIW+Xc2wMqek3ue2l1obxuq/WF0PU=", "last_login": null, "is_superuser": true, "username": "sinazk", "first_name": "", "last_name": "", "email": "", "is_staff": true, "is_active": true, "date_joined": "2025-05-28T18:43:55.659Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 2, "fields": {"password": "pbkdf2_sha256$1000000$aw3XV80Fh1t6le4YgeuZjg$a8z0YZsNH4Kqy78LI4YL0PTiOGMxHG1JtOX1RTUCGuM=", "last_login": "2025-05-28T18:45:24.078Z", "is_superuser": true, "username": "Sinazk", "first_name": "", "last_name": "", "email": "", "is_staff": true, "is_active": true, "date_joined": "2025-05-28T18:45:00.156Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 3, "fields": {"password": "pbkdf2_sha256$1000000$VXz48WQCKXcbDnhSx23HNW$AHaqoA2vODL1As4/Z7svoyYCU60DJeGn3tLIs+tmDxE=", "last_login": "2025-05-30T17:55:41.052Z", "is_superuser": true, "username": "navid", "first_name": "", "last_name": "", "email": "", "is_staff": true, "is_active": true, "date_joined": "2025-05-29T10:31:02.847Z", "groups": [], "user_permissions": []}}, {"model": "contenttypes.contenttype", "pk": 1, "fields": {"app_label": "admin", "model": "logentry"}}, {"model": "contenttypes.contenttype", "pk": 2, "fields": {"app_label": "auth", "model": "permission"}}, {"model": "contenttypes.contenttype", "pk": 3, "fields": {"app_label": "auth", "model": "group"}}, {"model": "contenttypes.contenttype", "pk": 4, "fields": {"app_label": "auth", "model": "user"}}, {"model": "contenttypes.contenttype", "pk": 5, "fields": {"app_label": "contenttypes", "model": "contenttype"}}, {"model": "contenttypes.contenttype", "pk": 6, "fields": {"app_label": "sessions", "model": "session"}}, {"model": "contenttypes.contenttype", "pk": 7, "fields": {"app_label": "django_q", "model": "schedule"}}, {"model": "contenttypes.contenttype", "pk": 8, "fields": {"app_label": "django_q", "model": "task"}}, {"model": "contenttypes.contenttype", "pk": 9, "fields": {"app_label": "django_q", "model": "failure"}}, {"model": "contenttypes.contenttype", "pk": 10, "fields": {"app_label": "django_q", "model": "success"}}, {"model": "contenttypes.contenttype", "pk": 11, "fields": {"app_label": "django_q", "model": "ormq"}}, {"model": "contenttypes.contenttype", "pk": 12, "fields": {"app_label": "shop", "model": "category"}}, {"model": "contenttypes.contenttype", "pk": 13, "fields": {"app_label": "shop", "model": "contentcreator"}}, {"model": "contenttypes.contenttype", "pk": 14, "fields": {"app_label": "shop", "model": "downloadlink"}}, {"model": "contenttypes.contenttype", "pk": 15, "fields": {"app_label": "shop", "model": "minecraftserver"}}, {"model": "contenttypes.contenttype", "pk": 16, "fields": {"app_label": "shop", "model": "item"}}, {"model": "contenttypes.contenttype", "pk": 17, "fields": {"app_label": "shop", "model": "purchase"}}, {"model": "sessions.session", "pk": "8z9t5qpdiulj75ppfceak45gkr4nqqth", "fields": {"session_data": ".eJxVjMsOwiAUBf-FtSGVC7S4dO83kPsAqRqalHZl_HfbpAvdnpk5bxVxXUpcW5rjKOqiQJ1-N0J-proDeWC9T5qnuswj6V3RB236Nkl6XQ_376BgK1vtski_6Y48dRC8ZZIgOFDuvElsMAwI2TDwOVmwHpCyZW-od2SdBfX5AhgUOLY:1uKvsz:44avAhGPOvNHLj_RDZ3tWXK9EXENzU2fiLBvt0xvB4g", "expire_date": "2025-06-13T09:18:45.916Z"}}, {"model": "sessions.session", "pk": "is8amkyhy3ykovk5307fzlwdbtsdncws", "fields": {"session_data": ".eJxVjMsOwiAUBf-FtSGVC7S4dO83kPsAqRqalHZl_HfbpAvdnpk5bxVxXUpcW5rjKOqiQJ1-N0J-proDeWC9T5qnuswj6V3RB236Nkl6XQ_376BgK1vtski_6Y48dRC8ZZIgOFDuvElsMAwI2TDwOVmwHpCyZW-od2SdBfX5AhgUOLY:1uKaXb:uZzFtKSWRxPl0Xbk3AdJxPZcHpAID1hmDBy6ehWeYII", "expire_date": "2025-06-12T10:31:15.640Z"}}, {"model": "sessions.session", "pk": "j7ju0vbr0mn9gbz91kztfo9mbrbuggop", "fields": {"session_data": ".eJxVjMsOwiAUBf-FtSGVC7S4dO83kPsAqRqalHZl_HfbpAvdnpk5bxVxXUpcW5rjKOqiQJ1-N0J-proDeWC9T5qnuswj6V3RB236Nkl6XQ_376BgK1vtski_6Y48dRC8ZZIgOFDuvElsMAwI2TDwOVmwHpCyZW-od2SdBfX5AhgUOLY:1uL3xF:sKkRvN207pRA7ERDrX6L1wqp-MLjTkmHF8fhBoQlHCc", "expire_date": "2025-06-13T17:55:41.058Z"}}, {"model": "sessions.session", "pk": "rhs11jhpqr4lo0c9ur6jn51jb4gl1q13", "fields": {"session_data": ".eJxVjMEOwiAQRP-FsyGwgGs9eu83kGUBqRqalPZk_Hdp0oMe5jDzZuYtPG1r8VtLi5-iuAoQp98sED9T3UF8UL3Pkue6LlOQe0UetMlxjul1O7p_B4Va6esLaGLFWkFQGKKF7BJCJEbDedCIWZ3jYF1yRoNiA2wpO4NKd98lPl_XwDbn:1uKLmG:X-ryAsLjZLzkXbhdXwTrgcZjA-RxdUiQ4xJR3jwZpWI", "expire_date": "2025-06-11T18:45:24.086Z"}}, {"model": "django_q.task", "pk": "022ce82fca6a49d2804b3fea922c96f7", "fields": {"name": "utah-eight-california-three", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:21:24.341Z", "stopped": "2025-06-09T15:21:24.606Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "042aaa86b56a4e12b41630a841c99ee0", "fields": {"name": "alanine-march-edward-winner", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:56:23.128Z", "stopped": "2025-06-08T23:56:23.272Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0580e82863ce435e80b815edda38413b", "fields": {"name": "finch-pizza-saturn-neptune", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:41:15.634Z", "stopped": "2025-06-09T09:41:15.806Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0588d11b61984e1c8893b5c497aaa70b", "fields": {"name": "five-papa-jig-monkey", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:31:10.983Z", "stopped": "2025-06-09T01:31:11.086Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "073c58c1662844a98fd685287e1598e3", "fields": {"name": "ohio-oklahoma-white-bacon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:56:18.238Z", "stopped": "2025-06-08T20:56:18.375Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "07a5a562400c461d95e653755d7724fe", "fields": {"name": "ink-blossom-golf-virginia", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:31:33.437Z", "stopped": "2025-06-09T03:31:33.697Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "081e3b192cd449429b70a185df0b2d61", "fields": {"name": "don-whiskey-south-neptune", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:26:10.030Z", "stopped": "2025-06-09T01:26:10.276Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "096136de3b714f6b97680c6216cd272b", "fields": {"name": "magnesium-jupiter-monkey-sweet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:26:19.077Z", "stopped": "2025-06-09T12:26:19.287Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0d879f80cd17438390b0a964e873d86e", "fields": {"name": "muppet-bravo-victor-crazy", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:41:22.082Z", "stopped": "2025-06-09T16:41:22.274Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0ee7256b2a264c488cd519aa85e748ec", "fields": {"name": "oregon-arizona-vegan-single", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:46:36.216Z", "stopped": "2025-06-09T03:46:36.399Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "0f1e197c31a54f1d8d90e4be16aa706f", "fields": {"name": "lactose-single-five-oklahoma", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:41:20.297Z", "stopped": "2025-06-08T23:41:20.588Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1012053a95dd459aa7df5f8f39b2fe6c", "fields": {"name": "eleven-ne<PERSON>ska-oscar-bluebird", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:41:08.705Z", "stopped": "2025-06-09T15:41:08.938Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "107ff29acd3b4a4a99888cd7574a4fe1", "fields": {"name": "april-winner-florida-princess", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:21:10.967Z", "stopped": "2025-06-08T20:21:11.263Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "10957e407e1347009d26c5abcae81e0d", "fields": {"name": "robin-colorado-stream-delaware", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:06:07.886Z", "stopped": "2025-06-08T20:06:08.196Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "11ba0c468adb4ed8b2b308898b0cb2cd", "fields": {"name": "triple-sink-magnesium-wisconsin", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:01:20.414Z", "stopped": "2025-06-09T05:01:20.604Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "11fb783c49f04f5188f643de394c57b8", "fields": {"name": "pennsylvania-oregon-twenty-nevada", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:36:21.141Z", "stopped": "2025-06-09T12:36:21.419Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "129169a8977c4efbb296044984d4af87", "fields": {"name": "fanta-hot-jersey-leopard", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:01:08.218Z", "stopped": "2025-06-09T14:01:08.477Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "14d5a3db2002440982f8fb3d15539033", "fields": {"name": "river-kansas-romeo-uncle", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:36:32.588Z", "stopped": "2025-06-09T08:36:32.686Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1569c8bbd8594489ad47e0fcee7c0aa0", "fields": {"name": "avocado-four-winner-east", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:31:22.052Z", "stopped": "2025-06-09T02:31:22.313Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "15f144d2e9d04ef296d5bf0bb64e78b4", "fields": {"name": "king-maryland-winter-don", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:21:12.182Z", "stopped": "2025-06-09T14:21:12.422Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "17219f22c77749a8977b8f95bd50eb3f", "fields": {"name": "minnesota-fruit-winner-echo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:36:34.366Z", "stopped": "2025-06-09T03:36:34.656Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "172de9e9c0004f0a94cad37899ce9063", "fields": {"name": "edward-maryland-william-one", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:16:11.157Z", "stopped": "2025-06-09T14:16:11.454Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "183a6c261ccd43d4a6b6635b37634f21", "fields": {"name": "item-fish-cola-edward", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:36:14.601Z", "stopped": "2025-06-09T09:36:14.874Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "18bef8b61b364d10afc410905887cddb", "fields": {"name": "uranus-kentucky-venus-sixteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:01:12.419Z", "stopped": "2025-06-09T16:01:12.697Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "197e2747d90f47c3a4c9e1d39e83ac3e", "fields": {"name": "april-gee-hotel-mobile", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:56:30.346Z", "stopped": "2025-06-08T21:56:30.615Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "19f11b71083d4ef0b6fdaa6b496f1a64", "fields": {"name": "mango-minnesota-mike-hamper", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:11:07.223Z", "stopped": "2025-06-09T01:11:07.478Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "19f75d136f994e2aab75578a27113199", "fields": {"name": "nuts-montana-alaska-north", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:11:08.904Z", "stopped": "2025-06-08T20:11:09.177Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1c4498deb5034ad38a7d01b0f722caad", "fields": {"name": "california-dakota-fish-potato", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:46:21.247Z", "stopped": "2025-06-08T23:46:21.339Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1e9264b254454921878e99515e12bbde", "fields": {"name": "harry-lima-september-cold", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:51:35.624Z", "stopped": "2025-06-09T08:51:35.885Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1f26153c0c54459eb800fad53229c475", "fields": {"name": "bacon-march-nebraska-oxygen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:41:08.838Z", "stopped": "2025-06-08T22:41:09.034Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "1fdf50490ef0490b8257a7c0fd887738", "fields": {"name": "tango-romeo-potato-edward", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:56:11.693Z", "stopped": "2025-06-08T22:56:11.907Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "207a23be8eab41239409b3202c0ade0c", "fields": {"name": "sink-five-autumn-white", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:41:09.695Z", "stopped": "2025-06-09T06:41:09.858Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2387c2ba19784eeaa5e2188b7965011e", "fields": {"name": "tennis-sierra-steak-alaska", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:31:07.686Z", "stopped": "2025-06-09T06:31:07.889Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "24384d67dddd40e79da2d8090f94ecc1", "fields": {"name": "early-oregon-video-queen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:21:27.815Z", "stopped": "2025-06-09T00:21:28.062Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "270c7075db4049aca3a085bb602bc560", "fields": {"name": "cup-low-eleven-video", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:56:07.202Z", "stopped": "2025-06-09T13:56:07.357Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "271bd69faeba434c9bdc5047710e44b7", "fields": {"name": "lake-bulldog-friend-mirror", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:16:10.588Z", "stopped": "2025-06-09T09:16:10.733Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "274cff8e023c4d06949af919da3da0ee", "fields": {"name": "bravo-lake-video-oven", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:06:32.931Z", "stopped": "2025-06-09T06:06:33.121Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "293d3b5b7f484fb1a71bded2b4083b9d", "fields": {"name": "lactose-sixteen-pizza-bravo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:41:12.844Z", "stopped": "2025-06-09T01:41:13.152Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2a3f2843867e4680b1be1460019ac513", "fields": {"name": "low-emma-lithium-fourteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:26:36.792Z", "stopped": "2025-06-09T06:26:37.074Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2bcadb9727754a0b9d39d13c9fa18b23", "fields": {"name": "pasta-beer-floor-music", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:01:07.584Z", "stopped": "2025-06-09T09:01:07.807Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "2d1d55a315a5428db2e33515ad810919", "fields": {"name": "single-magazine-kilo-eighteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:36:19.338Z", "stopped": "2025-06-08T23:36:19.587Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "32e8744f89754d60a80067c699a9f586", "fields": {"name": "triple-speaker-arkansas-fifteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:46:23.177Z", "stopped": "2025-06-09T12:46:23.475Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "336208419f7b45ca9dbc9c6a427ff430", "fields": {"name": "blue-johnny-sweet-wolfram", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:41:27.339Z", "stopped": "2025-06-08T21:41:27.522Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "339235990d8d423f828a639a5404ca03", "fields": {"name": "august-yellow-washington-network", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:31:18.353Z", "stopped": "2025-06-08T23:31:18.521Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "33c2b141533d4d248125b8a88c5c70c1", "fields": {"name": "alabama-april-purple-idaho", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:16:19.282Z", "stopped": "2025-06-09T02:16:19.503Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "33faf6735b174f11a6ff364040fd3cda", "fields": {"name": "four-batman-dakota-hawaii", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:26:25.311Z", "stopped": "2025-06-09T15:26:25.527Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "341222829a8c4c45bb6454219157e01e", "fields": {"name": "massachusetts-carbon-oxygen-east", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:01:13.706Z", "stopped": "2025-06-09T07:01:13.975Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "35649739b59c4f5fb721183c8ca01bf9", "fields": {"name": "zulu-echo-potato-solar", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:56:31.065Z", "stopped": "2025-06-09T05:56:31.371Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "358d893ad92e4c39a6fcca8de3dc1037", "fields": {"name": "asparagus-mango-butter-blue", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:51:10.524Z", "stopped": "2025-06-09T15:51:10.710Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "393326907b3f4723ae363b75bac9b323", "fields": {"name": "romeo-cardinal-summer-twenty", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:51:24.210Z", "stopped": "2025-06-09T16:51:24.432Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "39b4bf64059842a7aa95442d2311fc5d", "fields": {"name": "green-leopard-green-moon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:51:17.213Z", "stopped": "2025-06-08T20:51:17.430Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3bdf14cf60c846f5838be9df75059e83", "fields": {"name": "enemy-bulldog-emma-july", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:11:21.411Z", "stopped": "2025-06-08T21:11:21.526Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3be53b1662ce49708d1670d23b5bdb8f", "fields": {"name": "whiskey-muppet-echo-don", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:06:27.273Z", "stopped": "2025-06-09T13:06:27.545Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3ccb53acd3ac4caca074242797f5955c", "fields": {"name": "arizona-november-sweet-papa", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:51:18.268Z", "stopped": "2025-06-09T14:51:18.542Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3cded39c6da64bfda47118ce925a001b", "fields": {"name": "pasta-louisiana-alaska-stairway", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:36:15.648Z", "stopped": "2025-06-09T04:36:15.894Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3d80931f20f548a8ad5b9e7b3c76085e", "fields": {"name": "delaware-double-beer-burger", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:46:29.095Z", "stopped": "2025-06-09T05:46:29.322Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3e4c67189ad744ec814687a70452c10b", "fields": {"name": "august-uncle-uniform-nineteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:21:35.243Z", "stopped": "2025-06-08T22:21:35.508Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3e52a9ac57c64c50bb223f25042fe991", "fields": {"name": "jersey-mississippi-march-georgia", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:06:28.721Z", "stopped": "2025-06-09T03:06:29.040Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3e79a275132643f993780f05281de922", "fields": {"name": "mockingbird-mobile-triple-zebra", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:26:24.337Z", "stopped": "2025-06-08T21:26:24.609Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3efde5df9a784363a2d6a3857c7eebb5", "fields": {"name": "wolfram-seven-gee-helium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:51:17.578Z", "stopped": "2025-06-09T09:51:17.799Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "3fb3eec4736547ad80d69dacccc9baca", "fields": {"name": "oranges-yankee-island-lake", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:16:34.306Z", "stopped": "2025-06-08T22:16:34.473Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "432c0f7fba2b4cdda18b627d9b168714", "fields": {"name": "carbon-ack-double-california", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:11:25.972Z", "stopped": "2025-06-09T00:11:26.247Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "44486653a8894ec69b3ac7fd62c30666", "fields": {"name": "echo-oven-nine-september", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:51:07.102Z", "stopped": "2025-06-09T03:51:07.395Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "44981657af1b408db591a0c4e5b1422b", "fields": {"name": "nitrogen-lima-equal-eleven", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:26:18.626Z", "stopped": "2025-06-09T07:26:18.887Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "45511f8826ec4a5891cfca2d21d66f1d", "fields": {"name": "march-table-river-maryland", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:01:20.327Z", "stopped": "2025-06-09T15:01:20.578Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "45812763f17742b4b7b72c7bf5f6292d", "fields": {"name": "magnesium-kentucky-grey-april", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:36:15.195Z", "stopped": "2025-06-09T14:36:15.458Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "45eff4f1d01d4871bddf0c1a93832a93", "fields": {"name": "pip-white-lactose-oven", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:21:31.584Z", "stopped": "2025-06-09T03:21:31.735Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "48012c47149d437da2e8245704ff5ba2", "fields": {"name": "carpet-quiet-echo-alanine", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:16:26.887Z", "stopped": "2025-06-09T00:16:27.067Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "488fc803fd0e49ed83b0ca2ead162819", "fields": {"name": "aspen-grey-sweet-mike", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:36:26.730Z", "stopped": "2025-06-09T10:36:27.045Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "49ca3b8f1ad54ebaada3173f7c3ec8af", "fields": {"name": "earth-eight-chicken-charlie", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:31:20.120Z", "stopped": "2025-06-09T12:31:20.270Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "4a56d50d1bcf45338521f3520fbd055e", "fields": {"name": "shade-ohio-angel-utah", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:01:26.260Z", "stopped": "2025-06-09T13:01:26.369Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "4aef363c6c414296952003eb3a5d557e", "fields": {"name": "pip-wolfram-high-four", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:26:36.140Z", "stopped": "2025-06-08T22:26:36.325Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "4bfa12da7f0e4fcf9c663eac9500dc40", "fields": {"name": "lima-washington-item-arkansas", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:31:14.660Z", "stopped": "2025-06-09T04:31:14.769Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "4d9116dd4ba544cb95baec7ab614e6c7", "fields": {"name": "blossom-idaho-river-march", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:26:28.740Z", "stopped": "2025-06-09T00:26:28.956Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "4e8c06d1418846a285a64553afc2fa69", "fields": {"name": "beryllium-crazy-double-zebra", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:51:29.753Z", "stopped": "2025-06-09T10:51:30.109Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "4fc88298b20a46c5b1ca1f550fca8baa", "fields": {"name": "nitrogen-eight-delaware-two", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:21:35.802Z", "stopped": "2025-06-09T06:21:35.992Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "52bdda99aeaa4177822bae9cf0b239c1", "fields": {"name": "paris-delta-nine-robert", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:16:16.665Z", "stopped": "2025-06-09T07:16:16.915Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "53b3f0b8814544b7ac514b83f62977dd", "fields": {"name": "oscar-earth-double-lake", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:46:34.614Z", "stopped": "2025-06-09T08:46:34.752Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "553f17290ae642a0a1438e7542797fde", "fields": {"name": "glucose-autumn-bluebird-nevada", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:11:27.538Z", "stopped": "2025-06-09T08:11:27.651Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "55566e13585745b5a6829331a502e9e8", "fields": {"name": "low-zulu-massachusetts-oranges", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:11:21.532Z", "stopped": "2025-06-09T10:11:21.663Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "57101861c48d4da7a5af975e8e06b6fa", "fields": {"name": "emma-oklahoma-seventeen-shade", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:01:16.537Z", "stopped": "2025-06-09T02:01:16.815Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "57a1fe2f67cc4ee280f536b2702f46fb", "fields": {"name": "coffee-april-washington-twelve", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:46:10.830Z", "stopped": "2025-06-09T11:46:10.914Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "59abcac3dfa5427f90d93c052d32ab1e", "fields": {"name": "xray-five-kentucky-oxygen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:46:16.615Z", "stopped": "2025-06-09T09:46:16.873Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "59c254ea2ca84707901941d9b79a22f9", "fields": {"name": "charlie-shade-bakerloo-winner", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:16:09.962Z", "stopped": "2025-06-08T20:16:10.226Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "59ca7461d0984b899b704f455d1af253", "fields": {"name": "massachusetts-mike-triple-undress", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:01:13.962Z", "stopped": "2025-06-09T12:01:14.079Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5b3436896218430fab18ea174086cce9", "fields": {"name": "spaghetti-early-foxtrot-twenty", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:01:35.380Z", "stopped": "2025-06-09T01:01:35.461Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5b46fa204d51492eb5da95e6e62d8fb3", "fields": {"name": "sink-lithium-burger-winter", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:51:29.323Z", "stopped": "2025-06-08T21:51:29.558Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5baf9126c0af4c4ea2cb5d2fb72b377c", "fields": {"name": "fifteen-july-bulldog-sweet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:51:10.714Z", "stopped": "2025-06-08T22:51:10.894Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5be5ca3cbcb74e6b95574bb9a3e70dfe", "fields": {"name": "fix-cup-december-quiet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:01:19.551Z", "stopped": "2025-06-09T10:01:19.810Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5c1972f9cac64c859feb7c712897fed2", "fields": {"name": "solar-sierra-lima-moon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:11:14.217Z", "stopped": "2025-06-09T16:11:14.311Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5c45e28bafd04572a97316b9afbd13a1", "fields": {"name": "kentucky-fix-lake-nuts", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:21:20.189Z", "stopped": "2025-06-09T02:21:20.362Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5d15633a545d40a18ea3012f7bdf86ed", "fields": {"name": "bakerloo-uranus-alaska-south", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:41:22.137Z", "stopped": "2025-06-09T12:41:22.282Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "5d2ba82d471544c38646f6906d6b48af", "fields": {"name": "whiskey-stream-oranges-triple", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:31:13.604Z", "stopped": "2025-06-09T09:31:13.883Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "627fd29a77b244fcbf60faa2d77fc98c", "fields": {"name": "golf-louisiana-may-victor", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:56:11.469Z", "stopped": "2025-06-09T15:56:11.569Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "62bea420d87e4e2e93849b716e2a92c3", "fields": {"name": "hamper-single-yellow-beryllium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:21:11.590Z", "stopped": "2025-06-09T09:21:11.834Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "646e0b11bbe44138a3232ae59abed701", "fields": {"name": "bacon-chicken-freddie-west", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:41:34.289Z", "stopped": "2025-06-09T13:41:34.547Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "64eed534426d4baf99a20b1c0803d435", "fields": {"name": "johnny-snake-connecticut-vegan", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:06:32.760Z", "stopped": "2025-06-09T11:06:32.981Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "65ba4ef685d7448c963877b681961cd8", "fields": {"name": "island-nevada-kansas-stairway", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:06:20.372Z", "stopped": "2025-06-08T21:06:20.640Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "65d3b31f78ef410791f45ee20daf9769", "fields": {"name": "bravo-speaker-tango-helium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:31:31.561Z", "stopped": "2025-06-09T08:31:31.821Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "663d7ed2b1da44a2b6c3b0c0f815d787", "fields": {"name": "winter-mississippi-angel-rugby", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:46:17.563Z", "stopped": "2025-06-09T04:46:17.748Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "66c0ffc01b4e406e9cae956144068986", "fields": {"name": "network-lithium-single-fourteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:51:36.323Z", "stopped": "2025-06-09T13:51:36.431Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "671c74e994524217941439b54d0547c1", "fields": {"name": "undress-moon-aspen-solar", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:06:13.642Z", "stopped": "2025-06-08T23:06:13.960Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "69742b80a0414f3fa40b1189adb87642", "fields": {"name": "queen-monkey-earth-carolina", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:41:35.296Z", "stopped": "2025-06-09T03:41:35.543Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6a637e56e65b48f0b4b6fd43a8bfd6a8", "fields": {"name": "carolina-angel-romeo-jersey", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:01:27.772Z", "stopped": "2025-06-09T03:01:27.879Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6b92328431df4d3693e21730ed5fc2d5", "fields": {"name": "freddie-muppet-green-orange", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:31:29.653Z", "stopped": "2025-06-09T00:31:29.838Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6c4b0020907749fa9ca623b8c5bf5140", "fields": {"name": "april-glucose-october-johnny", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:21:29.558Z", "stopped": "2025-06-09T08:21:29.873Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6c7be0d74b9348b3a55725c7fe22b83a", "fields": {"name": "cardinal-cat-bacon-hot", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:46:17.251Z", "stopped": "2025-06-09T14:46:17.565Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6d1b5fe785ac4a7db3145c881aadb780", "fields": {"name": "solar-california-kitten-magnesium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:21:24.225Z", "stopped": "2025-06-09T05:21:24.306Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6e6697057bba4ad4a7f5db484a015d08", "fields": {"name": "nevada-hotel-september-california", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:31:13.085Z", "stopped": "2025-06-08T20:31:13.372Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6f80f538d5464b77accd4f5b2f155b45", "fields": {"name": "carpet-pip-lake-ceiling", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:56:30.773Z", "stopped": "2025-06-09T10:56:30.868Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "6fdd879e9d3a4d719f5bd70581ff2044", "fields": {"name": "papa-nine-black-bravo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:21:23.573Z", "stopped": "2025-06-09T10:21:23.810Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "703e5b26ebb5490783e7758c61eb4e2d", "fields": {"name": "diet-bacon-nitrogen-utah", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:26:31.270Z", "stopped": "2025-06-09T13:26:31.493Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "71b73c794a20498891ca0e48cdcd881d", "fields": {"name": "march-papa-butter-muppet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:11:18.354Z", "stopped": "2025-06-09T02:11:18.474Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "72db582f5dcf40dd88b2577fdbf9c74a", "fields": {"name": "thirteen-avocado-batman-pip", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:06:25.039Z", "stopped": "2025-06-09T00:06:25.289Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "735f9747b0eb42589ff22557c5858375", "fields": {"name": "yankee-failed-carbon-queen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:46:22.580Z", "stopped": "2025-06-09T07:46:22.843Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "753d8ff991fe495281685879680cf5b3", "fields": {"name": "emma-lactose-six-carpet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:46:16.228Z", "stopped": "2025-06-08T20:46:16.366Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "76113655d4cb4b739a52a6d0bacc7239", "fields": {"name": "alpha-coffee-sad-eleven", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:11:16.109Z", "stopped": "2025-06-09T12:11:16.210Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "764bc04b71324b688db471b4290e06bf", "fields": {"name": "queen-hydrogen-yankee-north", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:31:26.207Z", "stopped": "2025-06-09T05:31:26.318Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "775129d4030d4c1181c0f0b790617ea6", "fields": {"name": "tennessee-georgia-apart-cold", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:16:22.379Z", "stopped": "2025-06-08T21:16:22.525Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "79cdbfba8e4c440eab556f5031c1c638", "fields": {"name": "pluto-mexico-september-autumn", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:46:28.340Z", "stopped": "2025-06-08T21:46:28.578Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7a71ef7c5502440c99ec61b8324b646f", "fields": {"name": "neptune-carbon-paris-king", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:41:31.576Z", "stopped": "2025-06-09T00:41:31.823Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7a7222dd0e9b4d87b2ee1940201c6197", "fields": {"name": "winter-hotel-angel-skylark", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:21:35.785Z", "stopped": "2025-06-09T11:21:35.874Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7ac4ea36821b4534b23f2f70a1a8ad1b", "fields": {"name": "illinois-uranus-spring-salami", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:16:11.840Z", "stopped": "2025-06-09T04:16:12.127Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7ae7837306ef40ae8f70828c231821cb", "fields": {"name": "july-april-washington-speaker", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:16:23.259Z", "stopped": "2025-06-09T05:16:23.515Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7b601e249b304ceb9e6320fbe2b16251", "fields": {"name": "carolina-aspen-chicken-hydrogen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:56:24.553Z", "stopped": "2025-06-09T07:56:24.713Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7cdad39f513c4b7488023ee2d1334e8e", "fields": {"name": "vegan-gee-green-carpet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:36:33.294Z", "stopped": "2025-06-09T13:36:33.542Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7d3775289f034b8f9c687df3ab54ac7c", "fields": {"name": "blossom-hamper-lion-dakota", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:11:33.356Z", "stopped": "2025-06-08T22:11:33.561Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7de606381ae3443d8d31eb8eca49332b", "fields": {"name": "orange-magnesium-tango-nine", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:26:13.685Z", "stopped": "2025-06-09T04:26:13.801Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7e3ee406af9a4dbca53cf971d4239b39", "fields": {"name": "ohio-shade-bakerloo-golf", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:21:16.465Z", "stopped": "2025-06-08T23:21:16.591Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "7fb2ce0474d74813ae872a4591c32416", "fields": {"name": "april-wisconsin-fish-hydrogen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:06:08.582Z", "stopped": "2025-06-09T09:06:08.817Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8099e507794545f98c6ede5ba1b33d5f", "fields": {"name": "xray-magnesium-island-kentucky", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:01:09.009Z", "stopped": "2025-06-09T04:01:09.281Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "809a28b0d17a41ef84837867af6a515a", "fields": {"name": "mango-angel-blue-solar", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:41:27.754Z", "stopped": "2025-06-09T10:41:27.886Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "83529897afea4f419644e989dbb01b58", "fields": {"name": "thirteen-football-potato-cola", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:46:32.514Z", "stopped": "2025-06-09T00:46:32.594Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "85bec37c34574aadbb1860c01a3671a4", "fields": {"name": "mars-mars-alaska-wolfram", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:21:18.100Z", "stopped": "2025-06-09T12:21:18.268Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "87c9c727c3e943f8b42b421182df22ec", "fields": {"name": "pennsylvania-monkey-social-moon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:26:25.231Z", "stopped": "2025-06-09T05:26:25.376Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "887dd8a7b9ce40feaeaba1fcd4e6a663", "fields": {"name": "mike-sixteen-hamper-winter", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:21:17.636Z", "stopped": "2025-06-09T07:21:17.773Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8a7ad85a360240d7a2683c7bf79296b4", "fields": {"name": "king-oklahoma-mississippi-five", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:56:19.305Z", "stopped": "2025-06-09T14:56:19.526Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8b00b79338b34481aa4fb778925cb6bf", "fields": {"name": "pip-foxtrot-colorado-sink", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:01:19.295Z", "stopped": "2025-06-08T21:01:19.492Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8be1a83e1400431c895f252f954a2da0", "fields": {"name": "yankee-football-tennis-gee", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:36:11.910Z", "stopped": "2025-06-09T01:36:12.100Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8c7056a57ad345cb8eae4f015d7b9b14", "fields": {"name": "avocado-coffee-jupiter-oven", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:46:24.958Z", "stopped": "2025-06-09T02:46:25.128Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "8d6d706f3e40491cbcf546ebe565a973", "fields": {"name": "zulu-comet-two-harry", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:06:09.961Z", "stopped": "2025-06-09T04:06:10.081Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "90a043067bc34ba7ba50a5f452fc7c21", "fields": {"name": "lake-glucose-robin-whiskey", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:56:34.447Z", "stopped": "2025-06-09T00:56:34.709Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "94ffa784a5204e4b892052476fc58be7", "fields": {"name": "fix-magnesium-romeo-single", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:01:31.774Z", "stopped": "2025-06-09T11:01:32.041Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9696e5ffb08a4e01ae35004e514c8dfd", "fields": {"name": "bravo-lake-stream-july", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:06:17.442Z", "stopped": "2025-06-09T02:06:17.579Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "96c44b2266ed4473aba312e475f520a1", "fields": {"name": "eight-romeo-zebra-alabama", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:31:06.896Z", "stopped": "2025-06-09T15:31:07.095Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "974079a5ffc7460e9a0e99b26cb1f514", "fields": {"name": "asparagus-kentucky-salami-eighteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:06:09.209Z", "stopped": "2025-06-09T14:06:09.496Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "97f99d613e644aa4bbad4675b7b7ee7e", "fields": {"name": "montana-purple-charlie-mockingbird", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:18:17.587Z", "stopped": "2025-06-09T16:18:17.861Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9ae0c2e0e2884efabe0ec01cc77e839f", "fields": {"name": "hamper-thirteen-juliet-orange", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:26:17.429Z", "stopped": "2025-06-08T23:26:17.593Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9be52d5798974b3696781713ae53b3cf", "fields": {"name": "alpha-king-vegan-maine", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:16:28.551Z", "stopped": "2025-06-09T08:16:28.726Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9c593f229dfc421d932deb3b780a8de0", "fields": {"name": "table-emma-jupiter-carbon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:11:10.884Z", "stopped": "2025-06-09T04:11:11.013Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9c8202c762834456b7a4eea63bddc95a", "fields": {"name": "tennessee-whiskey-hawaii-lake", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:16:29.258Z", "stopped": "2025-06-09T13:16:29.565Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9c8387ed0cd74c0bbbd6c87ad6abb35c", "fields": {"name": "lake-nineteen-three-montana", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:16:22.547Z", "stopped": "2025-06-09T10:16:22.758Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9d62670ea3f745aea63b7cf48559db20", "fields": {"name": "music-salami-beryllium-coffee", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:36:23.004Z", "stopped": "2025-06-09T02:36:23.254Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9d66d65c32a84ec6997c9291e5cf7ac0", "fields": {"name": "kilo-batman-undress-mockingbird", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:11:09.589Z", "stopped": "2025-06-09T09:11:09.689Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9d746212e95c4dc0a3dfc4bc8eddabe7", "fields": {"name": "network-east-alpha-carbon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:06:20.531Z", "stopped": "2025-06-09T10:06:20.754Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "9f73f8ef8dee46df8b089d4439fe8e4d", "fields": {"name": "xray-wyoming-harry-alpha", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:41:23.979Z", "stopped": "2025-06-09T02:41:24.156Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a124ec4e578d43eb838de60ea2715aa4", "fields": {"name": "coffee-king-undress-crazy", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:26:13.134Z", "stopped": "2025-06-09T14:26:13.328Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a1378502bcb346c39d62a5a9909a9e13", "fields": {"name": "bakerloo-mike-whiskey-may", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:51:18.517Z", "stopped": "2025-06-09T04:51:18.660Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a1dd5d73d6ff44d79a2d09dc12ad1bd4", "fields": {"name": "gee-robert-illinois-kentucky", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:26:11.992Z", "stopped": "2025-06-08T20:26:12.158Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a2911e6a878c45ada77866bf90acaf61", "fields": {"name": "five-twenty-apart-west", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:26:30.559Z", "stopped": "2025-06-09T08:26:30.813Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a2de4513e8af44529db6733898f2afbf", "fields": {"name": "colorado-fruit-hydrogen-lion", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:11:15.671Z", "stopped": "2025-06-09T07:11:15.917Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a57018126e144b8e8d4bc677ad190e8f", "fields": {"name": "three-salami-lemon-double", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:46:09.638Z", "stopped": "2025-06-09T15:46:09.790Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a60e54f1e1344d91bde0ef17237eb49e", "fields": {"name": "august-arkansas-orange-lemon", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:31:07.715Z", "stopped": "2025-06-09T11:31:07.825Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a85db386297a456c9f216a669b324dd1", "fields": {"name": "saturn-lion-potato-double", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:11:22.359Z", "stopped": "2025-06-09T15:11:22.488Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a89355bbcc9a4700b4fe7a7b99c62ec5", "fields": {"name": "stream-bakerloo-football-purple", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:11:10.180Z", "stopped": "2025-06-09T14:11:10.327Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a9132f37bce940aaade01f6e61bd86c6", "fields": {"name": "ohio-salami-eighteen-nineteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:41:16.247Z", "stopped": "2025-06-09T14:41:16.485Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a9492171b5c64d0a89ab2cf852f870b1", "fields": {"name": "pizza-don-winner-johnny", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:41:15.206Z", "stopped": "2025-06-08T20:41:15.497Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "a97e2bd5ce1b47f19290ac688c653e39", "fields": {"name": "cold-india-single-video", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:36:20.588Z", "stopped": "2025-06-09T07:36:20.723Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "aab3b78e17664fc9b6916564686912a5", "fields": {"name": "cardinal-winter-chicken-queen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:46:09.817Z", "stopped": "2025-06-08T22:46:10.049Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ab939531e0914cb68c569d344f09dc11", "fields": {"name": "nineteen-minnesota-kitten-minnesota", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:56:12.879Z", "stopped": "2025-06-09T11:56:13.178Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "abd2c9ffca044e008f6c08fa876f6650", "fields": {"name": "freddie-magnesium-bakerloo-thirteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:56:12.692Z", "stopped": "2025-06-09T06:56:12.919Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ac2a3eae5be343da8195079c62a95d71", "fields": {"name": "black-carolina-mobile-uniform", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:16:15.543Z", "stopped": "2025-06-08T23:16:15.654Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "adc27b98899048c7b1128c5c8e2c2782", "fields": {"name": "mirror-music-kitten-april", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:01:24.077Z", "stopped": "2025-06-09T00:01:24.323Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b043ef9b044f44318ae79db2c1ff364b", "fields": {"name": "may-emma-fanta-fifteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:36:26.359Z", "stopped": "2025-06-08T21:36:26.629Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b248f709fbd74ba79570fb547225bf17", "fields": {"name": "alpha-saturn-florida-zulu", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:51:22.204Z", "stopped": "2025-06-08T23:51:22.440Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b3912efef0314ede8bedbfb74840a59b", "fields": {"name": "west-fruit-jupiter-early", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:01:31.325Z", "stopped": "2025-06-08T22:01:31.455Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b47b4117ed124e4faed7931177000f8d", "fields": {"name": "network-zebra-xray-william", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:31:20.021Z", "stopped": "2025-06-09T16:31:20.224Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b4ca91ae6afb47aa8cf8482f7fb61887", "fields": {"name": "failed-london-beer-green", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:41:16.630Z", "stopped": "2025-06-09T04:41:16.864Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b523d2f10cc64309ab8996aa22f9eb5c", "fields": {"name": "quebec-magnesium-california-juliet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:06:14.691Z", "stopped": "2025-06-09T07:06:14.847Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b6bbf14b7c8644b794c074a9fcafd936", "fields": {"name": "quiet-arkansas-michigan-romeo", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:11:28.270Z", "stopped": "2025-06-09T13:11:28.518Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "b822e49f6bd34f8faabe77a2fe9d475a", "fields": {"name": "triple-leopard-september-low", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:26:12.580Z", "stopped": "2025-06-09T09:26:12.905Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "bafd02e5eee54e70865daeb52bbe8e71", "fields": {"name": "nuts-double-saturn-jig", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:31:06.946Z", "stopped": "2025-06-08T22:31:07.127Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "bb14c901f9b045bd8998eb5a884c67fb", "fields": {"name": "island-pluto-nuts-hamper", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:21:09.073Z", "stopped": "2025-06-09T01:21:09.310Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "bbcff36455de4413b04db8f67d5bfad0", "fields": {"name": "twenty-tennis-princess-aspen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:36:07.801Z", "stopped": "2025-06-09T15:36:08.100Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "bc9ff354cbff464db842d81a28f950e0", "fields": {"name": "mars-enemy-east-idaho", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:51:14.717Z", "stopped": "2025-06-09T01:51:15.014Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "bf097eacff5a4bfe9aa49886665aff87", "fields": {"name": "indigo-bacon-cardinal-fillet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:56:15.625Z", "stopped": "2025-06-09T01:56:15.725Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c1d023c9822b444ab08b42a5c61dc458", "fields": {"name": "xray-oregon-tennis-five", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:46:35.284Z", "stopped": "2025-06-09T13:46:35.498Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c2fec18fc03e4d5888e26e53a75df842", "fields": {"name": "king-venus-grey-equal", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:36:21.037Z", "stopped": "2025-06-09T16:36:21.141Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c36efd7d9eee473883ff6cf526f71476", "fields": {"name": "crazy-avocado-uncle-princess", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:06:13.329Z", "stopped": "2025-06-09T16:06:13.445Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c394296646c54b61884f37955ab59c32", "fields": {"name": "blue-oven-ink-failed", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:21:18.163Z", "stopped": "2025-06-09T16:21:18.263Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c3f6c1a2e8334ad7b11c2e7bbeee3c18", "fields": {"name": "green-fish-wisconsin-lactose", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:51:30.092Z", "stopped": "2025-06-09T05:51:30.253Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c577f5dc2186428286d580a7cd7eb500", "fields": {"name": "nine-island-lactose-apart", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:46:10.701Z", "stopped": "2025-06-09T06:46:10.920Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c7b799de6d4c4805aa1e417d0481a5f0", "fields": {"name": "earth-juliet-mexico-spaghetti", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:51:11.695Z", "stopped": "2025-06-09T06:51:11.917Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "c98a17b22a0943838c0915e499b14e8e", "fields": {"name": "undress-twenty-lactose-uranus", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:26:32.504Z", "stopped": "2025-06-09T03:26:32.735Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "cb4fac90f0d34e60965b1d63984fe9e6", "fields": {"name": "red-august-princess-table", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:06:26.527Z", "stopped": "2025-06-09T08:06:26.740Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "cc0ff2b5149a4a96b505ca427398dde7", "fields": {"name": "mars-gee-east-speaker", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:46:23.136Z", "stopped": "2025-06-09T16:46:23.418Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ce5b8f26fc39417b99d07da913e4dca7", "fields": {"name": "eighteen-zulu-nitrogen-mirror", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:26:24.606Z", "stopped": "2025-06-09T10:26:24.733Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ce9ae2e13d60400b8efc9e4d34c66b04", "fields": {"name": "grey-black-october-nitrogen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:36:07.895Z", "stopped": "2025-06-08T22:36:08.136Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d15101111a7d4e579141b1e163c75049", "fields": {"name": "mockingbird-low-magnesium-rugby", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:31:25.331Z", "stopped": "2025-06-08T21:31:25.515Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d1a37ad297c94693bf35d742e28437c6", "fields": {"name": "ten-minnesota-carbon-nebraska", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:36:08.748Z", "stopped": "2025-06-09T11:36:08.871Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d2029c03f6ef4e4dad62034fc50c59e4", "fields": {"name": "freddie-bravo-march-lactose", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:56:36.627Z", "stopped": "2025-06-09T08:56:36.825Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d38e9512727d418f8006abc53df67c41", "fields": {"name": "ten-september-uranus-winner", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:51:11.850Z", "stopped": "2025-06-09T11:51:12.081Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d52bca0e4e88472b9b5c65d7dc59a1be", "fields": {"name": "edward-oxygen-lake-nevada", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:16:34.823Z", "stopped": "2025-06-09T06:16:34.928Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d5e14d28324b4211911baeaafa12129d", "fields": {"name": "fruit-colorado-missouri-island", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:46:28.771Z", "stopped": "2025-06-09T10:46:28.935Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d62fa3d6667140e9a60ea6627206422e", "fields": {"name": "mirror-sad-juliet-blue", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:06:36.343Z", "stopped": "2025-06-09T01:06:36.496Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d65932e71444470c8b6c1bb6a0844179", "fields": {"name": "harry-bulldog-florida-burger", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:56:08.058Z", "stopped": "2025-06-09T03:56:08.285Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d7564f8a05c146829cc602b1eb60f8a1", "fields": {"name": "fillet-ack-utah-spring", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:56:19.473Z", "stopped": "2025-06-09T04:56:19.613Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d858a14f1c6e4408a541c4ec82e7581a", "fields": {"name": "jupiter-emma-social-coffee", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:56:25.204Z", "stopped": "2025-06-09T12:56:25.332Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "d8fb46d47dd340e2ba890ee76ccd32c6", "fields": {"name": "pluto-aspen-ten-golf", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:01:12.666Z", "stopped": "2025-06-08T23:01:12.759Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "da4bcf5b13324eab8df30bb4a0c54309", "fields": {"name": "angel-shade-september-dakota", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:36:27.170Z", "stopped": "2025-06-09T05:36:27.293Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "dc2f0f0369124927bc353f3ad42f2a38", "fields": {"name": "zulu-beryllium-mirror-utah", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:36:08.677Z", "stopped": "2025-06-09T06:36:08.851Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "de78b2c3716c4470bc89834776d4ca5b", "fields": {"name": "sweet-cold-washington-diet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T04:21:12.764Z", "stopped": "2025-06-09T04:21:13.028Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "df99d04c35e3483aa3ca335d594297b1", "fields": {"name": "ten-one-april-eleven", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T09:56:18.538Z", "stopped": "2025-06-09T09:56:18.718Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "dfe627e11ed344f8af1fdd97ffc6d7b2", "fields": {"name": "zulu-kilo-wyoming-helium", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:31:32.281Z", "stopped": "2025-06-09T13:31:32.449Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e0925b877ce9451e895b3422a77a241f", "fields": {"name": "pennsylvania-solar-shade-undress", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T10:31:25.654Z", "stopped": "2025-06-09T10:31:25.913Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e0b2f853bf004e43bcb7e148b248080d", "fields": {"name": "wolfram-potato-ohio-zulu", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:26:36.800Z", "stopped": "2025-06-09T11:26:36.881Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e1db60435e9a4e069740b5202c040cb3", "fields": {"name": "bluebird-mirror-fanta-nebraska", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T23:11:14.603Z", "stopped": "2025-06-08T23:11:14.836Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e232299972b741039398b045dc0b2664", "fields": {"name": "hydrogen-may-zebra-muppet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:51:24.192Z", "stopped": "2025-06-09T12:51:24.329Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e2b8630ea0e14d5fac392c8fe8c4c66e", "fields": {"name": "earth-georgia-early-mars", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:16:34.791Z", "stopped": "2025-06-09T11:16:35.020Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e37e39e0c0c74bb49fd08c0423b8c180", "fields": {"name": "fillet-wisconsin-sink-ten", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:31:19.619Z", "stopped": "2025-06-09T07:31:19.921Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e3ac33dfd8144bfe8939a9d55bb1c75b", "fields": {"name": "oklahoma-lima-social-lamp", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:01:31.997Z", "stopped": "2025-06-09T06:01:32.225Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e53511ce7d674b02bfa1129829409ada", "fields": {"name": "avocado-georgia-mountain-cold", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:41:33.607Z", "stopped": "2025-06-09T08:41:33.861Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e5acbc5c61314f9585ab4e66f49bf953", "fields": {"name": "oven-mike-apart-sierra", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:16:23.369Z", "stopped": "2025-06-09T15:16:23.522Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e6fa11c20c5048129b9ba342ffd0dd50", "fields": {"name": "south-apart-triple-ohio", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:11:33.772Z", "stopped": "2025-06-09T11:11:34.007Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e897408fc07546a2980c219b5cc4787e", "fields": {"name": "pizza-fruit-cup-north", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:46:13.775Z", "stopped": "2025-06-09T01:46:13.912Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e8fd5aa26d2d4c668b0201de4370ede5", "fields": {"name": "victor-jig-green-eight", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T16:26:19.051Z", "stopped": "2025-06-09T16:26:19.371Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "e9585da6145a49598c517615d42858dd", "fields": {"name": "florida-hot-sad-lima", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T14:31:14.179Z", "stopped": "2025-06-09T14:31:14.334Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ea58b2742dd74350b7ac7cd55f5d7570", "fields": {"name": "lactose-utah-potato-april", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T11:41:09.803Z", "stopped": "2025-06-09T11:41:10.042Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ec69397da48442369c9081edb5fdca31", "fields": {"name": "september-glucose-hydrogen-princess", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T15:06:21.347Z", "stopped": "2025-06-09T15:06:21.475Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ed659d6d06aa4650bef9e68bfc139d85", "fields": {"name": "leopard-robert-colorado-winter", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T01:16:08.139Z", "stopped": "2025-06-09T01:16:08.343Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "eda0a992ab484351abef1b49d1bdcb7a", "fields": {"name": "lamp-washington-black-texas", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:11:22.307Z", "stopped": "2025-06-09T05:11:22.520Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ef44ff67a19c4db0950bb3f926813447", "fields": {"name": "diet-saturn-steak-steak", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:16:17.111Z", "stopped": "2025-06-09T12:16:17.255Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f0527b9b9b39430097547d1152b1c971", "fields": {"name": "fanta-triple-pip-hawaii", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:51:33.489Z", "stopped": "2025-06-09T00:51:33.737Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f0a87fe59e36450b99d6a1b1f482cf9b", "fields": {"name": "seven-undress-hotel-carpet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T08:01:25.503Z", "stopped": "2025-06-09T08:01:25.643Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f130d724bda14ef5ace27ef968316aa3", "fields": {"name": "delta-oregon-sixteen-mockingbird", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:11:29.646Z", "stopped": "2025-06-09T03:11:29.752Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f396f29788b846b3b9a1d9a7d9730089", "fields": {"name": "ack-sierra-iowa-cat", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T22:06:32.360Z", "stopped": "2025-06-08T22:06:32.669Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f3a79b5b278348a5b087f9e28f760574", "fields": {"name": "mountain-floor-connecticut-mexico", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T06:11:33.863Z", "stopped": "2025-06-09T06:11:34.073Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f3d70b05eed045edb4da65e8e8a649f6", "fields": {"name": "colorado-music-twenty-washington", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T00:36:30.638Z", "stopped": "2025-06-09T00:36:30.871Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f58ae1c3dde74d3fa375bfed34fc4518", "fields": {"name": "high-fix-mars-muppet", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T13:21:30.278Z", "stopped": "2025-06-09T13:21:30.375Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f6219d923890457e804a3e55ec0f863a", "fields": {"name": "table-moon-october-hotel", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:41:28.139Z", "stopped": "2025-06-09T05:41:28.270Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f71c5cbac2034ccc8c8ce1531e594235", "fields": {"name": "august-failed-potato-december", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T12:06:15.056Z", "stopped": "2025-06-09T12:06:15.368Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f8c6aabf25a14fcda880a2cfaf700f21", "fields": {"name": "comet-apart-finch-washington", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:51:23.548Z", "stopped": "2025-06-09T07:51:23.680Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f98cdd10471e42a1892a1e1c40e844a1", "fields": {"name": "red-robert-october-fourteen", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:51:25.862Z", "stopped": "2025-06-09T02:51:26.047Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "f994e41a3a424e958484973b8097ecb9", "fields": {"name": "moon-oklahoma-paris-fanta", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T05:06:21.364Z", "stopped": "2025-06-09T05:06:21.499Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "faa029c9e2564281bd260e75816d7d1a", "fields": {"name": "robert-lemon-timing-minnesota", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:56:26.826Z", "stopped": "2025-06-09T02:56:27.099Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "faaf9f0b6ab443dd9ecf3a9b595b1dea", "fields": {"name": "september-equal-virginia-william", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T07:41:21.602Z", "stopped": "2025-06-09T07:41:21.869Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "fcf9d1a2636a4b24aab5a2b47ee83769", "fields": {"name": "lamp-iowa-arkansas-six", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T03:16:30.601Z", "stopped": "2025-06-09T03:16:30.886Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "fe6ea39b28ac4b8384251c98a3b4940a", "fields": {"name": "oscar-four-charlie-michigan", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-09T02:26:21.104Z", "stopped": "2025-06-09T02:26:21.298Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ff2906eca65a47e4aba2079450404bd0", "fields": {"name": "eighteen-hotel-nevada-minnesota", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T21:21:23.370Z", "stopped": "2025-06-08T21:21:23.549Z", "success": true, "attempt_count": 1}}, {"model": "django_q.task", "pk": "ffba23dc9c1c46ef96b529a53629d12e", "fields": {"name": "robert-cold-pip-timing", "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "gAUpLg==", "kwargs": "gAV9lC4=", "result": 0, "group": "1", "cluster": "mcshop", "started": "2025-06-08T20:36:14.142Z", "stopped": "2025-06-08T20:36:14.446Z", "success": true, "attempt_count": 1}}, {"model": "django_q.schedule", "pk": 1, "fields": {"name": null, "func": "shop.tasks.timeout_unpaid_purchases", "hook": null, "args": "()", "kwargs": "{}", "schedule_type": "I", "minutes": 5, "repeats": -1, "next_run": "2025-06-09T16:56:06.836Z", "cron": null, "task": "393326907b3f4723ae363b75bac9b323", "cluster": null, "intended_date_kwarg": null}}, {"model": "shop.minecraftserver", "pk": 1, "fields": {"name": "ucraft-main", "display_name": "uCraft", "ip": "***********", "port": 25565, "domain": "play.ucraft.ir", "rcon_port": 25550, "query_port": 26577, "proxy": true, "enabled": true, "published": true}}, {"model": "shop.minecraftserver", "pk": 2, "fields": {"name": "ucraft-lobby", "display_name": "Game Lobby & Mini Games", "ip": "***********", "port": 25557, "domain": null, "rcon_port": 25551, "query_port": 25557, "proxy": false, "enabled": true, "published": true}}, {"model": "shop.minecraftserver", "pk": 3, "fields": {"name": "ucraft-survival", "display_name": "Survival", "ip": "***********", "port": 22557, "domain": null, "rcon_port": 22552, "query_port": 22557, "proxy": false, "enabled": true, "published": true}}, {"model": "shop.minecraftserver", "pk": 4, "fields": {"name": "ucraft-bedrwars", "display_name": "BedWars", "ip": "***********", "port": 22558, "domain": null, "rcon_port": 22553, "query_port": 22558, "proxy": false, "enabled": true, "published": true}}, {"model": "shop.downloadlink", "pk": 1, "fields": {"platform": "windows", "url": "https://dl2.tlauncher.org/f.php?f=files%2FTLauncher-Installer-1.8.0.exe", "enabled": true, "published": true}}, {"model": "shop.downloadlink", "pk": 2, "fields": {"platform": "android", "url": "https://dl2.tlauncher.org/f.php?f=files%2FTLauncher-Installer-1.8.0.exe", "enabled": true, "published": true}}, {"model": "shop.category", "pk": 1, "fields": {"name": "Survival", "display_name": "Survival", "description": "<PERSON><PERSON> hichi", "enabled": true, "published": true}}, {"model": "shop.category", "pk": 2, "fields": {"name": "BedWars", "display_name": "BedWars", "description": "Faghat BedWars", "enabled": true, "published": true}}, {"model": "shop.category", "pk": 3, "fields": {"name": "Ranks", "display_name": "Ranks", "description": "", "enabled": true, "published": true}}, {"model": "shop.contentcreator", "pk": 1, "fields": {"name": "<PERSON><PERSON>", "display_name": "Raiden", "description": "", "image": "images-57b5d356c3fb4e319c517f73a6fcc68c.jpg", "enabled": true, "published": true}}, {"model": "shop.contentcreator", "pk": 2, "fields": {"name": "<PERSON>", "display_name": "<PERSON>", "description": "", "image": "minecraft-removed-d5581f024afe40c783f5a6d5f43fe705.webp", "enabled": true, "published": true}}, {"model": "shop.contentcreator", "pk": 3, "fields": {"name": "SinaZK", "display_name": "SinaZK", "description": "3 sale az tehran", "image": "minecraft-banner-figures-f8cd5936a9e94ba3b5f22fc51fd75cd7.png", "enabled": true, "published": true}}, {"model": "shop.item", "pk": 4, "fields": {"name": "rank-iron-1m", "display_name": "رنک آیرون - یک ماهه", "description": "تمامی گیم مود ها\r\n\r\n\r\n- نمایش رنک آیرون در چت و لیست پلیر ها (تب لیست)\r\n- دریافت رول مخصوص آیرون در سرور دیسکورد تیرکس ماین\r\n\r\n\r\nلابی ها\r\n\r\n\r\n- گرفتن ۵۰ عدد Mystery Dust هر هفته به عنوان ریوارد رنک آیرون\r\n- تخفیف ۱۰٪ روی تمام آیتم های Cosmetic Menu\r\n\r\n\r\nسروایول\r\n\r\n\r\n- قابلیت جوین دادن به سروایول بعد از پر شدن سرور\r\n- قابلیت گرفتن کیت آیرون\r\n- قابلیت کلیم کردن تا 18 چانک\r\n- قابلیت شخصی کردن 5 بلاک\r\n- قابلیت شخصی کردن 5 ماب\r\n- قابلیت تراست کردن 3 نفر به وسایل شخصی سازی شده\r\n- قابلیت گذاشتن 7 عدد هاپر در هر چانک\r\n- قابلیت گذاشتن 15 عدد چست در هر چانک\r\n- قابلیت گذاشتن 15 عدد آیتم رداستونی در هر چانک\r\n- قابلیت انتخاب ۲ تا شغل (Job) همزمان\r\n- دسترسی به Crafting Table همراه با دستور workbench/\r\n- قابلیت برگشتن به مختصات قبلی با دستور back/\r\n- قابلیت انتخاب 2 تا home با دستور sethome/\r\n-قابلیت فروش همزمان 2 آیتم با دستور ah/\r\n\r\n\r\nاسکای بلاک\r\n\r\n\r\n- قابلیت جوین دادن به اسکای بلاک بعد از پر شدن سرور\r\n- قابلیت گرفتن کیت آیرون\r\n- قابلیت شخصی کردن 5 بلاک\r\n- قابلیت شخصی کردن 3 ماب\r\n- قابلیت باز کردن پت شاپ\r\n- قابلیت تراست کردن 3 نفر به وسایل شخصی سازی شده\r\n- دسترسی به Crafting Table همراه با دستور workbench/\r\n- قابلیت برگشتن به مختصات قبلی با دستور back/\r\n- قابلیت گذاشتن 2 کاستوم انچنت روی آیتم با دستورce/\r\n- قابلیت انتخاب 2 تا home با دستور sethome/\r\n- قابلیت فروش همزمان 2 آیتم با دستور ah/\r\n\r\n\r\nاف اف ای\r\n\r\n\r\n- دسترسی به کازمتیک های مربوط به رنک آیرون\r\n- داشتن تخفیف مخصوص رنک آیرون بر روی کازمتیک ها\r\n\r\n\r\nکریتیو\r\n\r\n\r\n-قابلیت دیناید کردن 6 عدد پلیر از پلات\r\n-قابلیت ادد کردن 6 عدد پلیر به پلات\r\n-قابلیت تراست کردن 6 عدد پلیر به پلات", "image": "iron-rank-f638f65b45eb4662b32f1ffdbee80c54.webp", "category": 3, "price": 79000, "ucoin_price": 79, "commands": "", "revoke_commands": "", "minecraft_server": 2, "expiration_days": 30, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 5, "fields": {"name": "rank-gold-1m", "display_name": "رن<PERSON> گلد - ۱ ماهه", "description": "تمامی گیم مود ها\r\n\r\n\r\n- نمایش رنک گلد در چت و لیست پلیر ها (تب لیست)\r\n- دریافت رول مخصوص گلد در سرور دیسکورد تیرکس ماین\r\n\r\n\r\nلابی ها\r\n\r\n\r\n- گرفتن ۵۰ عدد Mystery Dust هر هفته به عنوان ریوارد رنک آیرون\r\n- گرفتن ۱۰۰ عدد Mystery Dust هر هفته به عنوان ریوارد رنک گولد\r\n- قابلیت باز کردن ۲۰ تا Mystery Box به طور همزمان\r\n- قابلیت پرواز کردن با کامند /fly\r\n- تخفیف ۲۰٪ روی تمام آیتم های Cosmetic Menu\r\n- قابلیت فرستادن پیام رنگی تو چت\r\n\r\n\r\nسروایول\r\n\r\n\r\n- قابلیت پرواز کردن با کامند /fly\r\n- قابلیت /smithingtable\r\n- قابلیت گرفتن کیت گولد\r\n- قابلیت جوین دادن به سروایول بعد از پر شدن سرور\r\n- قابلیت گرفتن کیت آیرون\r\n- قابلیت کلیم کردن تا 24 چانک\r\n- قابلیت شخصی کردن 7 بلاک\r\n- قابلیت شخصی کردن 5 ماب\r\n- قابلیت تراست کردن 4 نفر به وسایل شخصی سازی شده\r\n- قابلیت گذاشتن 7 عدد هاپر در هر چانک\r\n- قابلیت گذاشتن 15 عدد چست در هر چانک\r\n- قابلیت گذاشتن 15 عدد آیتم رداستونی در هر چانک\r\n- قابلیت انتخاب 3 تا شغل (Job) همزمان\r\n- دسترسی به Crafting Table همراه با دستور workbench/\r\n- قابلیت برگشتن به مختصات قبلی با دستور back/\r\n- قابلیت انتخاب 3 تا home با دستور sethome/\r\n-قابلیت فروش همزمان 3 آیتم با دستور ah/\r\n\r\n\r\nاسکای بلاک\r\n\r\n\r\n- قابلیت پرواز کردن با کامند /fly\r\n- قابلیت جوین دادن به اسکای بلاک بعد از پر شدن سرور\r\n- قابلیت /smithingtable\r\n- قابلیت گرفتن کیت گولد\r\n- قابلیت گرفتن کیت آیرون\r\n- قابلیت شخصی کردن 7 بلاک\r\n- قابلیت شخصی کردن 5 ماب\r\n- قابلیت تراست کردن 4 نفر به وسایل شخصی سازی شده\r\n- قابلیت باز کردن پت شاپ\r\n- قابلیت باز کردن شاپ farming\r\n- قابلیت باز کردن شاپ mobdrops\r\n- دسترسی به Crafting Table همراه با دستور workbench/\r\n- قابلیت برگشتن به مختصات قبلی با دستور back/\r\n- قابلیت گذاشتن 2 کاستوم انچنت روی آیتم با دستورce/\r\n- قابلیت انتخاب 2 تا home با دستور sethome/\r\n- قابلیت فروش همزمان 2 آیتم با دستور ah/\r\n\r\n\r\nاف اف ای\r\n\r\n\r\n- دسترسی به کازمتیک های مربوط به رنک گولد\r\n- داشتن تخفیف مخصوص رنک گولد بر روی کازمتیک ها\r\n- دسترسی به کازمتیک های مربوط به رنک آیرون\r\n- داشتن تخفیف مخصوص رنک آیرون بر روی کازمتیک ها\r\n\r\n\r\nکریتیو\r\n\r\n\r\n-قابلیت دیناید کردن 7 عدد پلیر از پلات\r\n-قابلیت ادد کردن 7 عدد پلیر به پلات\r\n-قابلیت تراست کردن 7 عدد پلیر به پلات", "image": "gold-rank-2c1f09177f874373ab0d74d15f21e994.webp", "category": 3, "price": 149000, "ucoin_price": 149, "commands": "", "revoke_commands": "", "minecraft_server": 2, "expiration_days": 30, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 6, "fields": {"name": "rank-dia-1m", "display_name": "رنک دیاموند - ۱ ماهه", "description": "", "image": "diamond-rank-fc49ff842281487ba00864a4cde9f448.webp", "category": 3, "price": 189000, "ucoin_price": 189, "commands": "say hello", "revoke_commands": "say bye", "minecraft_server": 2, "expiration_days": 30, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 7, "fields": {"name": "rank-emral-1m", "display_name": "رن<PERSON> امرالد - ۱ ماهه", "description": "", "image": "emerald-rank-6b4e5ddfe81b42a0b8ff030c9abfbeb2.webp", "category": 3, "price": 349000, "ucoin_price": 349, "commands": "", "revoke_commands": "", "minecraft_server": 2, "expiration_days": 30, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 8, "fields": {"name": "rank-fal-20d", "display_name": "رنک فال - ۲۰ روز", "description": "", "image": "fall-rank-01f58134c7a64f2aa9b9deb1521fa50a.webp", "category": 3, "price": 569000, "ucoin_price": 569, "commands": "", "revoke_commands": "", "minecraft_server": 2, "expiration_days": 20, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 9, "fields": {"name": "survival-key-5k-common", "display_name": "۵ کلید Common", "description": "شما با خرید این محصول پنج عدد کلید جعبه \" Common \" بدست میارید\r\n\r\nجزئیات : این جعبه شامل آیتم های زیر هست:\r\n15% شانس برای گرفتن یک عدد Common Helmet\r\n15% شانس برای گرفتن یک عدد Common Chestplate\r\n15% شانس برای گرفتن یک عدد Common Leggings\r\n15% شانس برای گرفتن یک عدد Common Boots\r\n15% شانس برای گرفتن یک عدد Common Sword\r\n15% شانس برای گرفتن یک عدد Common Pickaxe\r\n15% شانس برای گرفتن یک عدد Common Axe\r\n15% شانس برای گرفتن یک عدد Common Shovel\r\n15% شانس برای گرفتن یک عدد Common Bow\r\n20% شانس برای گرفتن 32 عدد Gold Block\r\n25% شانس برای گرفتن 32 عدد Iron Block\r\n10% شانس برای گرفتن 16 عدد Diamond Ore\r\n10% شانس برای گرفتن 16 عدد Emerald Ore\r\n20% شانس برای گرفتن 5 عدد تخم Creeper\r\n15% شانس برای گرفتن 10 عدد تخم Blaze\r\n5% شانس برای گرفتن یک عدد Chicken Spawner\r\n5% شانس برای گرفتن یک عدد Sheep Spawner\r\n20% شانس برای گرفتن 16 عدد Ender Pearl\r\n30% شانس برای گرفتن 1000 XP\r\n25% شانس برای گرفتن 2500 XP\r\n\r\nنحوه استفاده : برای استفاده کردن از این محصول کافیست درگیم مود سروایول به warp crates برید و با در دست گرفتن کلید تون روی جعبه مورد نظر راست کلیک کنید", "image": "common-************************************.webp", "category": 1, "price": 58000, "ucoin_price": 58, "commands": "", "revoke_commands": "", "minecraft_server": null, "expiration_days": null, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 10, "fields": {"name": "survival-key-5k-rare", "display_name": "۵ کلید Rare", "description": "", "image": "rare-************************************.webp", "category": 1, "price": 99000, "ucoin_price": 99, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 11, "fields": {"name": "survival-key-5k-money", "display_name": "۵ کلید Money", "description": "", "image": "money-************************************.webp", "category": 1, "price": 147000, "ucoin_price": 147, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 12, "fields": {"name": "survival-cube-5*5", "display_name": "کیوب 5x5 Survival", "description": "قابلیت لینک کردن کیوب (نیازمند رنک دایمند یا بالاتر)\r\nقابلیت Sell all (نیازمند رنک دایمند یا بالاتر)\r\nقابلیت Rebuild\r\nقابلیت Smelt all (نیازمند رنک تیرکس یا بالاتر)\r\nقابلیت Compressor\r\nقابلیت Storage\r\nقابلیت Upgrades\r\n\r\nقابلیت آپگرید کردن Air percentage تا 3 لول\r\nقابلیت آپگرید کردن Quality تا 3 لول\r\nقابلیت آپگرید کردن Storage تا 3 لول", "image": "SrMineCube5-image-2023-01-05-8299609f21f84fcebdb6e5cb94ff2adc.webp", "category": 1, "price": 146000, "ucoin_price": 146, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 13, "fields": {"name": "survival-cube-15*15", "display_name": "کیوب 15x15 Survival", "description": "قابلیت لینک کردن کیوب (نیازمند رنک دایمند یا بالاتر)\r\nقابلیت Sell all (نیازمند رنک دایمند یا بالاتر)\r\nقابلیت Rebuild\r\nقابلیت Smelt all (نیازمند رنک تیرکس یا بالاتر)\r\nقابلیت Compressor\r\nقابلیت Storage\r\nقابلیت Upgrades\r\n\r\nقابلیت آپگرید کردن Air percentage تا 4 لول\r\nقابلیت آپگرید کردن Quality تا 3 لول\r\nقابلیت آپگرید کردن Storage تا 3 لول", "image": "SrMineCube15-image-2023-01-05-501c34af4d8a49d8ac1c1e97b2f2f3fa.webp", "category": 1, "price": 299000, "ucoin_price": 299, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 14, "fields": {"name": "survival-key-5k-spawner", "display_name": "۵ کلید Spawner", "description": "", "image": "spawner-************************************.webp", "category": 1, "price": 189000, "ucoin_price": 188, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": null, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 15, "fields": {"name": "survival-key-5k-legendary", "display_name": "۵ ک<PERSON><PERSON>د Legendary", "description": "", "image": "legendary-************************************.webp", "category": 1, "price": 199000, "ucoin_price": 199, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": null, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 16, "fields": {"name": "survival-key-5k-aegis", "display_name": "۵ ک<PERSON><PERSON><PERSON>is", "description": "", "image": "immortal-************************************.webp", "category": 1, "price": 493000, "ucoin_price": 493, "commands": "", "revoke_commands": "", "minecraft_server": 3, "expiration_days": null, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 17, "fields": {"name": "bedwars-private-1m", "display_name": "بازی خصوصی - ۱ ماهه", "description": "دسترسی یک ماهه به بازی های خصوصی", "image": "battlepass_GDWgCZn-35a9a3bd16d94b5a864fd247a2b24a3c.webp", "category": 2, "price": 319000, "ucoin_price": 319, "commands": "", "revoke_commands": "", "minecraft_server": 4, "expiration_days": 30, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 18, "fields": {"name": "bedwars-token-50k", "display_name": "۵۰k سکه", "description": "", "image": "bw-50k_MqSaqxy-535e47e0fdfa49cea49f37733850a68d.webp", "category": 2, "price": 99000, "ucoin_price": 99, "commands": "", "revoke_commands": "", "minecraft_server": 4, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 19, "fields": {"name": "bedwars-token-100k", "display_name": "100K tokens", "description": "", "image": "bw-100k_BLvlAH5-298d2e0570c94bfeab1b352b07d3f56b.webp", "category": 2, "price": 189000, "ucoin_price": 186, "commands": "", "revoke_commands": "", "minecraft_server": 4, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.item", "pk": 20, "fields": {"name": "bedwars-token-200k", "display_name": "200K tokens", "description": "", "image": "bw-200k_RUHadlg-131b82aa5469447f918123928e91fc42.webp", "category": 2, "price": 339000, "ucoin_price": 339, "commands": "", "revoke_commands": "", "minecraft_server": 4, "expiration_days": 0, "enabled": true, "published": true}}, {"model": "shop.purchase", "pk": 14, "fields": {"minecraft_username": "Sinazk", "mobile_number": "+989127379177", "item": 4, "state": "failed", "referrer": null, "created_at": "2025-05-30T10:57:32.606Z", "expires_at": null, "payment_succeeded_at": null, "subscription_status": "onetime", "ref_id": null, "authority": "A0000000000000000000000000006qy3ej5e", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 15, "fields": {"minecraft_username": "Sinazk", "mobile_number": "+989127379177", "item": 16, "state": "timeout", "referrer": null, "created_at": "2025-05-30T22:43:38.126Z", "expires_at": null, "payment_succeeded_at": null, "subscription_status": "onetime", "ref_id": null, "authority": "A000000000000000000000000000xqxnrwjz", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 16, "fields": {"minecraft_username": "Sinazk", "mobile_number": "09127379177", "item": 16, "state": "timeout", "referrer": null, "created_at": "2025-05-30T22:43:47.324Z", "expires_at": null, "payment_succeeded_at": null, "subscription_status": "onetime", "ref_id": null, "authority": "A000000000000000000000000000de3q8pm5", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 17, "fields": {"minecraft_username": "raiden", "mobile_number": "+989024500575", "item": 13, "state": "failed", "referrer": null, "created_at": "2025-05-31T05:03:06.935Z", "expires_at": null, "payment_succeeded_at": null, "subscription_status": "onetime", "ref_id": null, "authority": "A0000000000000000000000000003zgrqno7", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 18, "fields": {"minecraft_username": "raiden", "mobile_number": "+989024500575", "item": 10, "state": "timeout", "referrer": null, "created_at": "2025-05-31T20:46:05.971Z", "expires_at": null, "payment_succeeded_at": null, "subscription_status": "onetime", "ref_id": "69168302", "authority": "A000000000000000000000000000oxwyz157", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}, {"model": "shop.purchase", "pk": 19, "fields": {"minecraft_username": "Sinazk", "mobile_number": "09127379177", "item": 16, "state": "timeout", "referrer": null, "created_at": "2025-05-31T20:50:13.093Z", "expires_at": null, "payment_succeeded_at": null, "subscription_status": "onetime", "ref_id": "32510431", "authority": "A000000000000000000000000000l325zdre", "zarinpal_ref_id": null, "zarinpal_verify_response": null, "zarinpal_code": null}}]