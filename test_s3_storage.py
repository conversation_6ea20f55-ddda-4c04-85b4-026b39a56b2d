#!/usr/bin/env python
"""
Test script to verify Arvan S3 storage functionality.
Run this script to test if the S3 storage is working correctly.
"""

import os
import sys
import django
from io import BytesIO
from PIL import Image

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.storage import ArvanS3Storage


def create_test_image():
    """Create a simple test image in memory."""
    # Create a simple 100x100 red image
    img = Image.new('RGB', (100, 100), color='red')
    img_bytes = BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    return img_bytes


def test_s3_storage():
    """Test S3 storage functionality."""
    print("Testing Arvan S3 Storage...")
    
    try:
        # Initialize storage
        storage = ArvanS3Storage()
        print("✓ Storage initialized successfully")
        
        # Create test image
        test_image = create_test_image()
        test_filename = "test_image.png"
        
        # Test upload
        print(f"Uploading test image: {test_filename}")
        saved_name = storage.save(test_filename, test_image)
        print(f"✓ Image uploaded successfully as: {saved_name}")
        
        # Test exists
        if storage.exists(saved_name):
            print("✓ File exists check passed")
        else:
            print("✗ File exists check failed")
            return False
        
        # Test URL generation
        url = storage.url(saved_name)
        print(f"✓ Generated URL: {url}")
        
        # Test size
        try:
            size = storage.size(saved_name)
            print(f"✓ File size: {size} bytes")
        except Exception as e:
            print(f"⚠ Size check failed: {e}")
        
        # Test delete
        print("Deleting test file...")
        storage.delete(saved_name)
        print("✓ File deleted successfully")
        
        # Verify deletion
        if not storage.exists(saved_name):
            print("✓ File deletion verified")
        else:
            print("✗ File still exists after deletion")
            return False
        
        print("\n🎉 All tests passed! S3 storage is working correctly.")
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_s3_storage()
    sys.exit(0 if success else 1)
