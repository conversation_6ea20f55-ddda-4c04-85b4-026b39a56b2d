#!/usr/bin/env python
"""
Test script to verify automatic S3 image cleanup functionality.
Tests image deletion when:
1. Image is updated with a new image
2. Image is cleared (set to None)
3. Model is deleted
"""

import os
import sys
import django
from io import BytesIO
from PIL import Image
from django.core.files.uploadedfile import SimpleUploadedFile

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import Item, Category, ContentCreator
from shop.storage import ItemImageStorage, ContentCreatorImageStorage


def create_test_image(color='red', name_suffix=''):
    """Create a simple test image file."""
    img = Image.new('RGB', (100, 100), color=color)
    img_bytes = BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    return SimpleUploadedFile(
        name=f'test_{color}_image{name_suffix}.png',
        content=img_bytes.getvalue(),
        content_type='image/png'
    )


def check_file_exists_in_s3(storage, filename):
    """Check if a file exists in S3 storage."""
    try:
        return storage.exists(filename)
    except Exception:
        return False


def test_image_cleanup():
    """Test automatic image cleanup functionality."""
    print("Testing automatic S3 image cleanup...")
    
    # Initialize storage instances for checking file existence
    item_storage = ItemImageStorage()
    creator_storage = ContentCreatorImageStorage()
    
    try:
        # Test 1: Item image update
        print("\n1. Testing Item image update cleanup...")
        
        # Create category
        category = Category.objects.create(
            name='test_cleanup_category',
            display_name='Test Cleanup Category'
        )
        
        # Create item with first image
        first_image = create_test_image('red', '_first')
        item = Item.objects.create(
            name='test_cleanup_item',
            display_name='Test Cleanup Item',
            image=first_image,
            category=category,
            price=1000
        )
        first_image_name = item.image.name
        print(f"✓ Item created with first image: {first_image_name}")
        
        # Verify first image exists
        if check_file_exists_in_s3(item_storage, first_image_name):
            print("✓ First image exists in S3")
        else:
            print("✗ First image not found in S3")
            return False
        
        # Update with second image
        second_image = create_test_image('blue', '_second')
        item.image = second_image
        item.save()
        second_image_name = item.image.name
        print(f"✓ Item updated with second image: {second_image_name}")
        
        # Check that first image was deleted and second exists
        if not check_file_exists_in_s3(item_storage, first_image_name):
            print("✓ First image was automatically deleted from S3")
        else:
            print("✗ First image still exists in S3 (should be deleted)")
            return False
            
        if check_file_exists_in_s3(item_storage, second_image_name):
            print("✓ Second image exists in S3")
        else:
            print("✗ Second image not found in S3")
            return False
        
        # Test 2: Clear image (set to None)
        print("\n2. Testing image clearing cleanup...")
        item.image = None
        item.save()
        print("✓ Item image cleared (set to None)")
        
        # Check that second image was deleted
        if not check_file_exists_in_s3(item_storage, second_image_name):
            print("✓ Second image was automatically deleted when cleared")
        else:
            print("✗ Second image still exists in S3 (should be deleted)")
            return False
        
        # Test 3: ContentCreator image update
        print("\n3. Testing ContentCreator image update cleanup...")
        
        # Create creator with first image
        creator_first_image = create_test_image('green', '_creator_first')
        creator = ContentCreator.objects.create(
            name='test_cleanup_creator',
            display_name='Test Cleanup Creator',
            image=creator_first_image
        )
        creator_first_image_name = creator.image.name
        print(f"✓ ContentCreator created with first image: {creator_first_image_name}")
        
        # Update with second image
        creator_second_image = create_test_image('yellow', '_creator_second')
        creator.image = creator_second_image
        creator.save()
        creator_second_image_name = creator.image.name
        print(f"✓ ContentCreator updated with second image: {creator_second_image_name}")
        
        # Check cleanup
        if not check_file_exists_in_s3(creator_storage, creator_first_image_name):
            print("✓ ContentCreator first image was automatically deleted")
        else:
            print("✗ ContentCreator first image still exists (should be deleted)")
            return False
        
        # Test 4: Model deletion cleanup
        print("\n4. Testing model deletion cleanup...")
        
        # Delete creator (should delete the second image)
        creator.delete()
        print("✓ ContentCreator deleted")
        
        if not check_file_exists_in_s3(creator_storage, creator_second_image_name):
            print("✓ ContentCreator image was deleted when model was deleted")
        else:
            print("✗ ContentCreator image still exists after model deletion")
            return False
        
        # Clean up
        print("\n5. Cleaning up test data...")
        item.delete()  # Should not try to delete image since it's already None
        category.delete()
        print("✓ Test data cleaned up")
        
        print("\n🎉 All image cleanup tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_image_cleanup()
    sys.exit(0 if success else 1)
