#!/usr/bin/env python
"""
Test script to verify the refactored generic image cleanup signals.
"""

import os
import sys
import django
from io import BytesIO
from PIL import Image
from django.core.files.uploadedfile import SimpleUploadedFile

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import Item, Category, ContentCreator
from shop.storage import ItemImageStorage, ContentCreatorImageStorage


def create_test_image(color='red', name_suffix=''):
    """Create a simple test image file."""
    img = Image.new('RGB', (100, 100), color=color)
    img_bytes = BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    return SimpleUploadedFile(
        name=f'test_{color}_image{name_suffix}.png',
        content=img_bytes.getvalue(),
        content_type='image/png'
    )


def check_file_exists_in_s3(storage, filename):
    """Check if a file exists in S3 storage."""
    try:
        return storage.exists(filename)
    except Exception:
        return False


def test_generic_signals():
    """Test the refactored generic image cleanup signals."""
    print("Testing Generic Image Cleanup Signals...")
    print("=" * 50)
    
    item_storage = ItemImageStorage()
    creator_storage = ContentCreatorImageStorage()
    
    try:
        # Test 1: Item model
        print("\n1. Testing Item model with generic signals...")
        
        category = Category.objects.create(
            name='test_generic_category',
            display_name='Test Generic Category'
        )
        
        # Create item with image
        first_image = create_test_image('red', '_generic')
        item = Item.objects.create(
            name='test_generic_item',
            display_name='Test Generic Item',
            image=first_image,
            category=category,
            price=1000
        )
        first_image_name = item.image.name
        print(f"✓ Item created with image: {first_image_name}")
        
        # Update image
        second_image = create_test_image('blue', '_generic2')
        item.image = second_image
        item.save()
        second_image_name = item.image.name
        print(f"✓ Item updated with new image: {second_image_name}")
        
        # Verify old image was deleted
        if not check_file_exists_in_s3(item_storage, first_image_name):
            print("✓ Old image was automatically deleted")
        else:
            print("✗ Old image still exists")
            return False
        
        # Test 2: ContentCreator model
        print("\n2. Testing ContentCreator model with generic signals...")
        
        creator_image = create_test_image('green', '_creator_generic')
        creator = ContentCreator.objects.create(
            name='test_generic_creator',
            display_name='Test Generic Creator',
            image=creator_image
        )
        creator_image_name = creator.image.name
        print(f"✓ ContentCreator created with image: {creator_image_name}")
        
        # Clear creator image
        creator.image = None
        creator.save()
        print("✓ ContentCreator image cleared")
        
        # Verify image was deleted
        if not check_file_exists_in_s3(creator_storage, creator_image_name):
            print("✓ Creator image was automatically deleted when cleared")
        else:
            print("✗ Creator image still exists")
            return False
        
        # Test 3: Model deletion
        print("\n3. Testing model deletion...")
        
        # Add image back to item
        third_image = create_test_image('yellow', '_final')
        item.image = third_image
        item.save()
        third_image_name = item.image.name
        print(f"✓ Added final image to item: {third_image_name}")
        
        # Delete item
        item.delete()
        print("✓ Item deleted")
        
        # Verify image was deleted
        if not check_file_exists_in_s3(item_storage, third_image_name):
            print("✓ Final image was automatically deleted on model deletion")
        else:
            print("✗ Final image still exists")
            return False
        
        # Clean up
        creator.delete()
        category.delete()
        print("\n✓ Test data cleaned up")
        
        print("\n🎉 Generic signals test passed!")
        print("\n📋 Benefits of generic implementation:")
        print("   • Single codebase handles all models with images")
        print("   • Automatically discovers models with ImageField(s)")
        print("   • Easy to extend to new models")
        print("   • Supports multiple image fields per model")
        print("   • No code duplication")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_generic_signals()
    sys.exit(0 if success else 1)
