#!/usr/bin/env python
"""
Demonstration script showing automatic S3 image cleanup functionality.
This script shows how old images are automatically deleted when:
1. A new image is uploaded
2. An image is cleared
3. A model is deleted
"""

import os
import sys
import django
from io import BytesIO
from PIL import Image
from django.core.files.uploadedfile import SimpleUploadedFile

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import Item, Category


def create_demo_image(color='red'):
    """Create a simple demo image."""
    img = Image.new('RGB', (50, 50), color=color)
    img_bytes = BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    return SimpleUploadedFile(
        name=f'demo_{color}.png',
        content=img_bytes.getvalue(),
        content_type='image/png'
    )


def demo_automatic_cleanup():
    """Demonstrate automatic image cleanup."""
    print("🎯 Demonstrating Automatic S3 Image Cleanup")
    print("=" * 50)
    
    try:
        # Create a category
        category = Category.objects.create(
            name='demo_category',
            display_name='Demo Category'
        )
        
        # Step 1: Create item with red image
        print("\n1️⃣ Creating item with RED image...")
        red_image = create_demo_image('red')
        item = Item.objects.create(
            name='demo_item',
            display_name='Demo Item',
            image=red_image,
            category=category,
            price=1000
        )
        red_image_url = item.image.url
        print(f"   ✅ Item created with image: {red_image_url}")
        
        # Step 2: Update with blue image (red image will be auto-deleted)
        print("\n2️⃣ Updating item with BLUE image...")
        print("   🗑️ The RED image will be automatically deleted from S3")
        blue_image = create_demo_image('blue')
        item.image = blue_image
        item.save()
        blue_image_url = item.image.url
        print(f"   ✅ Item updated with new image: {blue_image_url}")
        print("   ✅ Old RED image was automatically deleted!")
        
        # Step 3: Clear image (blue image will be auto-deleted)
        print("\n3️⃣ Clearing the image...")
        print("   🗑️ The BLUE image will be automatically deleted from S3")
        item.image = None
        item.save()
        print("   ✅ Image cleared successfully!")
        print("   ✅ Old BLUE image was automatically deleted!")
        
        # Step 4: Add green image and delete model
        print("\n4️⃣ Adding GREEN image and deleting the item...")
        green_image = create_demo_image('green')
        item.image = green_image
        item.save()
        green_image_url = item.image.url
        print(f"   ✅ Item updated with GREEN image: {green_image_url}")
        
        print("   🗑️ Deleting the item (GREEN image will be auto-deleted)")
        item.delete()
        print("   ✅ Item deleted successfully!")
        print("   ✅ GREEN image was automatically deleted!")
        
        # Clean up
        category.delete()
        
        print("\n🎉 Demo completed successfully!")
        print("\n📋 Summary of automatic cleanup:")
        print("   • When image is updated → Old image deleted from S3")
        print("   • When image is cleared → Current image deleted from S3")
        print("   • When model is deleted → Current image deleted from S3")
        print("   • Only one image per model is kept at any time")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = demo_automatic_cleanup()
    sys.exit(0 if success else 1)
