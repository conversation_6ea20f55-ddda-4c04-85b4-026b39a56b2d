"""
Django signals for handling S3 image cleanup when models are updated or deleted.

This module provides a generic, reusable implementation that automatically:
1. Discovers all models in the app that have ImageField(s)
2. Registers cleanup signals for those models
3. Handles image deletion when:
   - Images are updated (old image deleted)
   - Images are cleared (current image deleted)
   - Models are deleted (all images deleted)

Benefits:
- Zero configuration: Just import this module and it works
- Extensible: Automatically works with new models that have ImageField(s)
- Multi-field support: Handles models with multiple image fields
- Error resilient: S3 errors don't break model operations
- DRY: Single implementation for all models

Usage:
Simply ensure this module is imported (via apps.py) and it will automatically
register cleanup signals for all models with ImageField(s) in the app.

For new models with images, no additional code is needed - the signals will
be automatically registered when the app starts.
"""

from django.db import models
from django.db.models.signals import pre_save, post_delete


def delete_old_image(old_image):
    """Helper function to delete an image file from S3 storage."""
    if old_image:
        try:
            # Delete the file from S3 storage
            old_image.delete(save=False)
        except Exception as e:
            # Log the error but don't raise it to avoid breaking the save operation
            print(f"Warning: Failed to delete old image file: {e}")


def get_image_fields(model_class):
    """Get all ImageField names from a model class."""
    image_fields = []
    for field in model_class._meta.get_fields():
        if isinstance(field, models.ImageField):
            image_fields.append(field.name)
    return image_fields


def handle_image_cleanup_on_save(sender, instance, **kwargs):
    """
    Generic function to handle image cleanup when a model instance is saved.
    Deletes old images when they are updated or cleared.
    """
    if not instance.pk:
        # This is a new instance, no old images to delete
        return

    try:
        # Get the current instance from database
        old_instance = sender.objects.get(pk=instance.pk)

        # Get all image fields for this model
        image_fields = get_image_fields(sender)

        for field_name in image_fields:
            old_image = getattr(old_instance, field_name, None)
            new_image = getattr(instance, field_name, None)

            # Check if image has changed or been cleared
            if old_image and (not new_image or old_image.name != new_image.name):
                delete_old_image(old_image)

    except sender.DoesNotExist:
        # Instance doesn't exist yet, nothing to clean up
        pass


def handle_image_cleanup_on_delete(sender, instance, **kwargs):
    """
    Generic function to handle image cleanup when a model instance is deleted.
    Deletes all images associated with the instance.
    """
    # Get all image fields for this model
    image_fields = get_image_fields(sender)

    for field_name in image_fields:
        image = getattr(instance, field_name, None)
        if image:
            delete_old_image(image)


def register_image_cleanup_signals(model_class):
    """
    Register image cleanup signals for a model class.
    This function can be called for any model that has ImageField(s).
    """
    # Create unique signal handler names to avoid conflicts
    model_name = model_class.__name__.lower()

    # Create pre_save handler
    def pre_save_handler(sender, instance, **kwargs):
        handle_image_cleanup_on_save(sender, instance, **kwargs)

    # Create post_delete handler
    def post_delete_handler(sender, instance, **kwargs):
        handle_image_cleanup_on_delete(sender, instance, **kwargs)

    # Set unique names for the handlers
    pre_save_handler.__name__ = f"{model_name}_image_cleanup_on_save"
    post_delete_handler.__name__ = f"{model_name}_image_cleanup_on_delete"

    # Register the signals
    pre_save.connect(pre_save_handler, sender=model_class)
    post_delete.connect(post_delete_handler, sender=model_class)


# Automatically register signals for all models with image fields
def auto_register_image_cleanup():
    """
    Automatically discover and register image cleanup signals for all models
    in the current app that have ImageField(s).
    """
    from django.apps import apps

    # Get all models from the current app
    app_models = apps.get_app_config('shop').get_models()

    for model_class in app_models:
        # Check if model has image fields
        image_fields = get_image_fields(model_class)
        if image_fields:
            register_image_cleanup_signals(model_class)
            # print(f"✅ Registered image cleanup signals for {model_class.__name__} (fields: {', '.join(image_fields)})")


# Register signals for all models with image fields
auto_register_image_cleanup()
