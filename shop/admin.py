from django.contrib import admin
from .models import MinecraftServer, Category, ContentCreator, Item, Purchase, DownloadLink


@admin.register(MinecraftServer)
class MinecraftServerAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'ip', 'port', 'domain', 'rcon_port', 'enabled', 'published', 'proxy']
    list_filter = ['enabled', 'published', 'proxy']
    search_fields = ['name', 'display_name', 'ip', 'domain']
    list_editable = ['enabled', 'published']


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'enabled', 'published']
    list_filter = ['enabled', 'published']
    search_fields = ['name', 'display_name']
    list_editable = ['enabled', 'published']


@admin.register(ContentCreator)
class ContentCreatorAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'enabled', 'published']
    list_filter = ['enabled', 'published']
    search_fields = ['name', 'display_name']
    list_editable = ['enabled', 'published']


@admin.register(Item)
class ItemAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'category', 'price', 'ucoin_price','expiration_days', 'minecraft_server', 'enabled', 'published']
    list_filter = ['category','expiration_days', 'minecraft_server', 'enabled', 'published']
    search_fields = ['name', 'display_name']
    list_editable = ['enabled', 'published']


@admin.register(Purchase)
class PurchaseAdmin(admin.ModelAdmin):
    list_display = ['ref_id', 'item', 'minecraft_username', 'state', 'subscription_status', 'created_at', 'payment_succeeded_at']
    list_filter = ['state', 'subscription_status', 'created_at', 'payment_succeeded_at']
    search_fields = ['ref_id', 'minecraft_username', 'mobile_number', 'zarinpal_ref_id']
    readonly_fields = ['ref_id', 'created_at', 'payment_succeeded_at', 'authority', 'zarinpal_ref_id', 'zarinpal_code', 'zarinpal_verify_response']
    date_hierarchy = 'created_at'


@admin.register(DownloadLink)
class DownloadLinkAdmin(admin.ModelAdmin):
    list_display = ['platform', 'url', 'enabled', 'published']
    list_filter = ['platform', 'enabled', 'published']
    search_fields = ['platform']
    list_editable = ['enabled', 'published']