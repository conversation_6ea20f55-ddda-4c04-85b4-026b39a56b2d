#!/usr/bin/env python
"""
Test script to verify Item model with S3 storage.
"""

import os
import sys
import django
from io import BytesIO
from PIL import Image
from django.core.files.uploadedfile import SimpleUploadedFile

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import Item, Category


def create_test_image():
    """Create a simple test image file."""
    # Create a simple 100x100 blue image
    img = Image.new('RGB', (100, 100), color='blue')
    img_bytes = BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    # Create Django file object
    return SimpleUploadedFile(
        name='test_item_image.png',
        content=img_bytes.getvalue(),
        content_type='image/png'
    )


def test_item_model():
    """Test Item model with S3 storage."""
    print("Testing Item model with S3 storage...")
    
    try:
        # Create a test category first
        category, created = Category.objects.get_or_create(
            name='test_category',
            defaults={
                'display_name': 'Test Category',
                'description': 'Test category for S3 storage testing'
            }
        )
        print(f"✓ Category {'created' if created else 'found'}: {category.name}")
        
        # Create test image
        test_image = create_test_image()
        
        # Create test item
        item = Item.objects.create(
            name='test_item_s3',
            display_name='Test Item with S3 Storage',
            description='This is a test item to verify S3 storage functionality',
            image=test_image,
            category=category,
            price=1000,
            ucoin_price=50
        )
        print(f"✓ Item created successfully: {item.name}")
        
        # Test image URL
        if item.image:
            image_url = item.image.url
            print(f"✓ Image URL generated: {image_url}")
            
            # Verify URL format
            if 'ucraft-shop/skus/' in image_url:
                print("✓ URL contains correct bucket and folder structure")
            else:
                print("✗ URL format incorrect")
                return False
        else:
            print("✗ No image attached to item")
            return False
        
        # Test image file name
        image_name = item.image.name
        print(f"✓ Image file name: {image_name}")
        
        # Clean up - delete the test item
        print("Cleaning up test data...")
        item.delete()  # This should also delete the S3 file
        print("✓ Test item deleted")
        
        # Clean up category if we created it
        if created:
            category.delete()
            print("✓ Test category deleted")
        
        print("\n🎉 Item model S3 integration test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_item_model()
    sys.exit(0 if success else 1)
